#!/bin/bash

# Set default values
default_password_length=12
default_special_characters='!@#$%^&*()_-+=<>?/[]{}|'

# Receive password length parameter, use default if not provided
password_length=${1:-$default_password_length}

# Receive special characters range parameter
special_characters=$2

# Generate password function
generate_password() {
    local characters='a-zA-Z0-9'

    # If special characters range is not empty, add it to the character set
    if [ -n "$special_characters" ]; then
        # Generate random string of length characters
        random_string=$(LC_ALL=C tr -dc "$characters" < /dev/random | head -c $password_length)

        # Select a random special character
        random_special_char=$(LC_ALL=C echo -n "$special_characters" | fold -w1 | awk 'BEGIN {srand()} {a[NR]=$0} END {print a[int(rand()*NR)+1]}')

        # Generate a random position to insert the special character
        insert_position=$((RANDOM % (password_length - 2) + 1))  # Ensure not in the first and last position

        # Use awk to insert the special character at the random position
        password=$(echo "$random_string" | awk -v pos=$insert_position -v special=$random_special_char '{a=substr($0, 1, pos) special substr($0, pos+1); print a}')
    else
        # Generate random string of length characters
        password=$(LC_ALL=C tr -dc "$characters" < /dev/random | head -c $password_length)
    fi

    # Output intermediate steps
    echo "Random String: $random_string"
    echo "Random Special Char: $random_special_char"
    echo "Insert Position: $insert_position"
    echo "Final Password: $password"
}

# Call the generate password function
generate_password
