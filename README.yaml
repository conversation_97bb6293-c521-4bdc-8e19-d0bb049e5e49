name: terraform-aws-rds
tags:
- aws
- terraform
- terraform-modules
- databases
- rds
- aws-rds
- postgres
- mysql
categories:
- terraform-modules/databases
license: APACHE2
github_repo: cloudposse/terraform-aws-rds
badges:
- name: Latest Release
  image: https://img.shields.io/github/release/cloudposse/terraform-aws-rds.svg
  url: https://github.com/cloudposse/terraform-aws-rds/releases/latest
- name: Slack Community
  image: https://slack.cloudposse.com/badge.svg
  url: https://slack.cloudposse.com
related:
- name: terraform-aws-rds-cluster
  description: Terraform module to provision an RDS Aurora cluster for MySQL or Postgres
  url: https://github.com/cloudposse/terraform-aws-rds-cluster
- name: terraform-aws-rds-cloudwatch-sns-alarms
  description: Terraform module that configures important RDS alerts using CloudWatch
    and sends them to an SNS topic
  url: https://github.com/cloudposse/terraform-aws-rds-cloudwatch-sns-alarms
description: Terraform module to provision AWS [`RDS`](https://aws.amazon.com/rds/)
  instances
introduction: |-
  The module will create:

  * DB instance (MySQL, Postgres, SQL Server, Oracle)
  * DB Option Group (will create a new one or you may use an existing)
  * DB Parameter Group
  * DB Subnet Group
  * DB Security Group
  * DNS Record in Route53 for the DB endpoint
usage: |-
  ```hcl
  module "rds_instance" {
      source = "cloudposse/rds/aws"
      # Cloud Posse recommends pinning every module to a specific version
      # version = "x.x.x"
      namespace                   = "eg"
      stage                       = "prod"
      name                        = "app"
      dns_zone_id                 = "Z89FN1IW975KPE"
      host_name                   = "db"
      security_group_ids          = ["sg-xxxxxxxx"]
      ca_cert_identifier          = "rds-ca-2019"
      allowed_cidr_blocks         = ["XXX.XXX.XXX.XXX/32"]
      database_name               = "wordpress"
      database_user               = "admin"
      database_password           = "xxxxxxxxxxxx"
      database_port               = 3306
      multi_az                    = true
      storage_type                = "gp2"
      allocated_storage           = 100
      storage_encrypted           = true
      engine                      = "mysql"
      engine_version              = "5.7.17"
      major_engine_version        = "5.7"
      instance_class              = "db.t2.medium"
      db_parameter_group          = "mysql5.7"
      option_group_name           = "mysql-options"
      publicly_accessible         = false
      subnet_ids                  = ["sb-xxxxxxxxx", "sb-xxxxxxxxx"]
      vpc_id                      = "vpc-xxxxxxxx"
      snapshot_identifier         = "rds:production-2015-06-26-06-05"
      auto_minor_version_upgrade  = true
      allow_major_version_upgrade = false
      apply_immediately           = false
      maintenance_window          = "Mon:03:00-Mon:04:00"
      skip_final_snapshot         = false
      copy_tags_to_snapshot       = true
      backup_retention_period     = 7
      backup_window               = "22:00-03:00"

      db_parameter = [
        { name  = "myisam_sort_buffer_size"   value = "1048576" },
        { name  = "sort_buffer_size"          value = "2097152" }
      ]

      db_options = [
        { option_name = "MARIADB_AUDIT_PLUGIN"
            option_settings = [
              { name = "SERVER_AUDIT_EVENTS"           value = "CONNECT" },
              { name = "SERVER_AUDIT_FILE_ROTATIONS"   value = "37" }
            ]
        }
      ]
  }
  ```
  ### Character Sets

  If you wish to create the database in a specific character set you can use one of the following options depending
  on your database engine of choice.

  For Oracle and Microsoft SQL you can specify charset name as an input variable
  to this module. For example, for Microsoft SQL, you could use:
  ```hcl
  module "rds_instance" {
    ...
    charset_name                   = "Korean_Wansung_CI_AS"
    ...
  }
  ```

  For `mysql` and `mariadb` engines character set of the database can be defined via `db_parameter`. In this example
  the database is created with `utf8mb4` (character set) and utf8mb4_unicode_ci (collation):

  ```hcl
  module "rds_instance" {
    ...
    db_parameter = [
      {
        name = "character_set_server"
        value = "utf8mb4"
        apply_method = "immediate"
      },
      {
        name = "collation_server"
        value = "utf8mb4_unicode_ci"
        apply_method = "immediate"
      }
    ]
    ...
  }
  ```
include:
- docs/targets.md
- docs/terraform.md
contributors:
- name: Erik Osterman
  github: osterman
- name: Andriy Knysh
  github: aknysh
- name: Sergey Vasilyev
  github: s2504s
- name: Valeriy
  github: drama17
- name: Konstantin B
  github: comeanother
- name: drmikecrowe
  github: drmikecrowe
- name: Oscar Sullivan
  github: osulli
- name: Federico Márquez
  github: fedemzcor
