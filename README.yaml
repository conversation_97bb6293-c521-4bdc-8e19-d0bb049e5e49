---
#
# This is the canonical configuration for the `README.md`
# Run `make readme` to rebuild the `README.md`
#

# Name of this project
name: terraform-aws-security-group

# Tags of this project
tags:
  - aws
  - security-group
  - terraform
  - terraform-modules

# Logo for this project
#logo: docs/logo.png

# License of this project
license: "APACHE2"

# Copyrights
copyrights:
  - name: "Cloud Posse, LLC"
    url: "https://cloudposse.com"
    year: "2021"

# Canonical GitHub repo
github_repo: cloudposse/terraform-aws-security-group

# Badges to display
badges:
  - name: "Latest Release"
    image: "https://img.shields.io/github/release/cloudposse/terraform-aws-security-group.svg"
    url: "https://github.com/cloudposse/terraform-aws-security-group/releases/latest"
  - name: "Slack Community"
    image: "https://slack.cloudposse.com/badge.svg"
    url: "https://slack.cloudposse.com"

# List any related terraform modules that this module may be used with or that this module depends on.
related:
  - name: "terraform-null-label"
    description: "Terraform module designed to generate consistent names and tags for resources. Use terraform-null-label to implement a strict naming convention."
    url: "https://github.com/cloudposse/terraform-null-label"

# List any resources helpful for someone to get started. For example, link to the hashicorp documentation or AWS documentation.
references:
  - name: terraform-provider-aws
    description: Terraform AWS provider
    url: https://registry.terraform.io/providers/hashicorp/aws/latest

# Short description of this project
description: |-
  Terraform module to create AWS Security Group and rules.

# Introduction to the project
#introduction: |-
#  This is an introduction.

# How to use this module. Should be an easy example to copy and paste.
usage: |-

  Note: Terraform requires that all the elements of the `rules` list be exactly
  the same type. This means you must supply all the same keys and, for each key,
  all the values for that key must be the same type. Any optional key, such as
  `ipv6_cidr_blocks`, can be omitted from all the rules without problem. However,
  if some rules have a key and other rules would omit the key if that were allowed
  (e.g one rule has `cidr_blocks` and another rule has `self = true`, and neither
  rule can include both `cidr_blocks` and `self`), instead of omitting the key,
  include the key with value of `null`, unless the value is a list type, in which case
  set the value to `[]` (an empty list).

  Although `description` is optional, if you do not include a description,
  the rule will be deleted and recreated if the index of the rule in the `rules`
  list changes, which usually happens as a result of adding or removing a rule. Rules
  that include a description will only be modified if the rule itself changes.
  Also, if 2 rules specify the same `type`, `protocol`, `from_port`, and `to_port`,
  they must not also have the same `description` (although if one or both rules
  have no description supplied, that will work).

  ```hcl
  module "label" {
    source = "cloudposse/label/null"
    # Cloud Posse recommends pinning every module to a specific version
    # version = "x.x.x"
    namespace  = "eg"
    stage      = "prod"
    name       = "bastion"
    attributes = ["public"]
    delimiter  = "-"

    tags = {
      "BusinessUnit" = "XYZ",
      "Snapshot"     = "true"
    }
  }

  module "vpc" {
    source = "cloudposse/vpc/aws"
    # Cloud Posse recommends pinning every module to a specific version
    # version = "x.x.x"
    cidr_block = "10.0.0.0/16"

    context = module.label.context
  }

  module "sg" {
    source = "cloudposse/security-group/aws"
    # Cloud Posse recommends pinning every module to a specific version
    # version = "x.x.x"
    
    vpc_id = module.vpc.vpc_id

    rules = [
      {
        type        = "ingress"
        from_port   = 22
        to_port     = 22
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
        self        = null
        description = "Allow SSH from anywhere"
      },
      {
        type        = "ingress"
        from_port   = 80
        to_port     = 80
        protocol    = "tcp"
        cidr_blocks = []
        self        = true
        description = "Allow HTTP from inside the security group"
      },

      {
        type        = "egress"
        from_port   = 0
        to_port     = 65535
        protocol    = "all"
        cidr_blocks = ["0.0.0.0/0"]
        self        = null
        description = "Allow egress to anywhere"
      }
    ]

    context = module.label.context
  }
  ```

# Example usage
examples: |-
  Here is an example of using this module:
  - [`examples/complete`](https://github.com/cloudposse/terraform-aws-security-group/examples/complete) - complete example of using this module

# How to get started quickly
#quickstart: |-
#  Here's how to get started...

# Other files to include in this README from the project folder
include:
  - "docs/targets.md"
  - "docs/terraform.md"

# Contributors to this project
contributors:
  - name: "Erik Osterman"
    github: "osterman"
  - name: "Vladimir"
    github: "SweetOps"
