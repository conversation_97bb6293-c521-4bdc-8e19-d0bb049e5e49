
aa:
	export NAKAMA_VERSION=3.18.0 && export DB_ADDRESS=3zNdtDtvA7GGKU7n:<EMAIL>:26257/defaultdb?sslmode=verify-full && docker-compose -f nakama.yml up -d



ssh-prod1:
	ssh -o StrictHostKeyChecking=no -i "ci/nakama_backend_prod.pem" ec2-user@**************

ssh-prod2:
	ssh -o StrictHostKeyChecking=no -i "ci/nakama_backend_prod.pem" ec2-user@**************

ssh-dev:
	ssh -o StrictHostKeyChecking=no -i "ci/nakama_backend_dev.pem" <EMAIL>

ssh-prod-test:
	ssh -o StrictHostKeyChecking=no -i "ci/nakama_backend_dev.pem" <EMAIL>


.PHONY: pwd

#### nkmmd
pwd-nkmmd-dev-console-pwd:
	./pwd_gen.sh 16 '^_-' > pwd_gen_nkmmd_dev_console_pwd_instance.sh
pwd-nkmmd-dev-console-username:
	./pwd_gen.sh 10 '' > pwd_gen_nkmmd_dev_console_username_instance.sh
pwd-nkmmd-rds-pwd:
	./pwd_gen.sh 22 '_' > pwd_gen_nkmmd_rds_pwd_instance.sh
pwd-nkmmd-prod-console-pwd:
	./pwd_gen.sh 16 '^_-' > pwd_gen_nkmmd_prod_console_pwd_instance.sh
pwd-nkmmd-prod-console-username:
	./pwd_gen.sh 10 '' > pwd_gen_nkmmd_prod_console_username_instance.sh

#### nkmad
pwd-nkmad-prod-console-pwd:
	./pwd_gen.sh 16 '^_-' > ./pwd_gen/ad/pwd_gen_nkmad_prod_console_pwd_instance.sh
pwd-nkmad-prod-console-username:
	./pwd_gen.sh 10 '' > ./pwd_gen/ad/pwd_gen_nkmad_prod_console_username_instance.sh
pwd-nkmad-prod-console-http-key:
	./pwd_gen.sh 22 '' > ./pwd_gen/ad/pwd_gen_nkmad_prod_console_http_key_instance.sh
pwd-nkmad-prod-server-key:
	./pwd_gen.sh 10 '' > ./pwd_gen/ad/pwd_gen_nkmad_prod_server_key_instance.sh

# 这是用于生成 github 直接更新 prod 节点 action 的 password 授权
pwd-nkmad-prod-github-action-prod-action-pwd:
	./pwd_gen.sh 10 '' > ./pwd_gen/ad/pwd_gen_nkmad_prod_github_action_prod_action_pwd_instance.sh


pwd-nkmad-prod-1panel-entrance:
	./pwd_gen.sh 10 '' > ./pwd_gen/ad/pwd_gen_nkmad_prod_1panel-entrance_instance.sh
pwd-nkmad-prod-1panel-user:
	./pwd_gen.sh 12 '' > ./pwd_gen/ad/pwd_gen_nkmad_prod_1panel-user_instance.sh
pwd-nkmad-prod-1panel-pwd:
	./pwd_gen.sh 17 '_' > ./pwd_gen/ad/pwd_gen_nkmad_prod_1panel-pwd_instance.sh

pwd-nkmad-prod-cockroach-db-user:
	./pwd_gen.sh 10 '' > ./pwd_gen/ad/pwd_gen_nkmad_prod_cockroach-db-user_instance.sh
pwd-nkmad-prod-cockroach-db-pwd:
	# 自动生成的 HJGku6mLf33SkKSd08bxHQ
	cat ./pwd_gen/ad/pwd_gen_nkmad_prod_cockroach-db-pwd_instance.sh

#### nkmfd
pwd-nkmfd-dev-console-pwd:
	./pwd_gen.sh 16 '^_-' > pwd_gen_nkmfd_dev_console_pwd_instance.sh
pwd-nkmfd-dev-console-username:
	./pwd_gen.sh 10 '' > pwd_gen_nkmfd_dev_console_username_instance.sh
pwd-nkmfd-rds-pwd:
	./pwd_gen.sh 22 '_' > pwd_gen_nkmfd_rds_pwd_instance.sh
pwd-nkmfd-prod-console-pwd:
	./pwd_gen.sh 16 '^_-' > pwd_gen_nkmfd_prod_console_pwd_instance.sh
pwd-nkmfd-prod-console-username:
	./pwd_gen.sh 10 '' > pwd_gen_nkmfd_prod_console_username_instance.sh
pwd-nkmfd-prod-console-http-key:
	./pwd_gen.sh 22 '' > pwd_gen_nkmfd_prod_console_http_key_instance.sh
pwd-nkmfd-dev-console-http-key:
	./pwd_gen.sh 22 '' > pwd_gen_nkmfd_dev_console_http_key_instance.sh


pwd-nkmfd-staging-console-pwd:
	./pwd_gen.sh 16 '^_-' > pwd_gen_nkmfd_staging_console_pwd_instance.sh
pwd-nkmfd-staging-console-username:
	./pwd_gen.sh 10 '' > pwd_gen_nkmfd_staging_console_username_instance.sh
pwd-nkmfd-staging-console-http-key:
	./pwd_gen.sh 22 '' > pwd_gen_nkmfd_staging_console_http_key_instance.sh


pwd-nkmmd-prod-github-action-prod-action-pwd:
	./pwd_gen.sh 10 '' > pwd_gen_nkmfd_prod_github_action_prod_action_pwd_instance.sh
.PHONY: nkmmd


ssh-prod1-nkmmd:
	ssh -o StrictHostKeyChecking=no -i "ci/nakama_backend_prod.pem" ec2-user@***********

ssh-prod2-nkmmd:
	ssh -o StrictHostKeyChecking=no -i "ci/nakama_backend_prod.pem" ec2-user@**************

ssh-dev-nkmmd:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmmd_dev.pem" <EMAIL>

gen-fingerprint-dev-nkmmd:
	#ssh dev.nkmmd.pwglab.com ssh-keygen -l -f /etc/ssh/ssh_host_ed25519_key.pub | cut -d ' ' -f2
	ssh dev.nkmmd.pwglab.com ssh-keygen -l -f /etc/ssh/ssh_host_rsa_key.pub | cut -d ' ' -f2
	#ssh dev.nkmmd.pwglab.com ssh-keygen -l -f /etc/ssh/ssh_host_ecdsa_key.pub | cut -d ' ' -f2

ssh-prod-nkmmd:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmmd_prod.pem" ec2-user@***********
	ssh -o StrictHostKeyChecking=no -i "ci/nkmmd_prod.pem" ec2-user@**************
	ssh -o StrictHostKeyChecking=no -i "ci/nkmmd_prod.pem" <EMAIL>

show:
	docker exec 22d886dc05d6 cat /nakama/data/logfile.log
	docker cp 22d886dc05d6:/nakama/data/logfile.log ./logfile.log
	scp -o StrictHostKeyChecking=no -i "ci/nkmmd_prod.pem" ec2-user@***********:~/logfile.log ./temp
	scp -o StrictHostKeyChecking=no -i "ci/nkmmd_prod.pem" ec2-user@**************:~/logfile.log ./temp
	docker exec ec2-user-nakama-1 tail -n 1000 /nakama/data/logfile.log > output.txt


.PHONY: nkmfd
update-pem-permission:
	chmod 400 ./ci/*.pem

ssh-dev-nkmfd:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_dev.pem" <EMAIL>

ssh-dev2-nkmfd:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_dev.pem" <EMAIL>

ssh-work-nkmfd:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_dev.pem" <EMAIL>

ssh-dev-nkmfd-copy-log:
	scp -o StrictHostKeyChecking=no -i "ci/nkmfd_dev.pem" <EMAIL>:~/data/logfile.log /Volumes/Xin/work/logs
	scp -o StrictHostKeyChecking=no -i "ci/nkmfd_dev.pem" <EMAIL>:~/data/logfile-2025-04-16T01-32-53.502.log /Volumes/Xin/work/logs


# does not work
gen-fingerprint-dev-nkmfd:
	#ssh dev.nkmfd.pwglab.com ssh-keygen -l -f /etc/ssh/ssh_host_ed25519_key.pub | cut -d ' ' -f2
	ssh dev.nkmfd.pwglab.com ssh-keygen -l -f /etc/ssh/ssh_host_rsa_key.pub | cut -d ' ' -f2
	#ssh dev.nkmfd.pwglab.com ssh-keygen -l -f /etc/ssh/ssh_host_ecdsa_key.pub | cut -d ' ' -f2


ssh-prod-nkmfd-node-0:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>

ssh-prod-nkmfd-node-0-odd:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>

ssh-prod-nkmfd-node-0-even:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>

ssh-prod-nkmfd-node-0-three:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>

ssh-prod-nkmfd-node-0-four:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>

# 下载当前版本的 index.js 用于对照  log 查看错误!, 修改版本号为当前 prod 环境最新, 建议使用个 s2s api 获取
nkmfdVer=1.0.32
ssh-prod-nkmfd-node-0-copy-index-from-docker:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL> "docker cp ec2-user-nakama-1:/nakama/data/modules/build/index.js index.${nkmfdVer}.js"

ssh-prod-nkmfd-node-0-copy-index:
	scp -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>:~/index.${nkmfdVer}.js  ./logs/index.nkmfd.${nkmfdVer}.js


prod-copy-log:
	scp -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>:~/logfile-big.log /Volumes/Xin/work/logs

prod-copy-log-odd:
	scp -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>:~/logfile-2024-11-20.log /Volumes/Xin/work/logs/odd-logfile-2024-11-20.log
prod-copy-log-even:
	scp -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>:~/logfile-2024-11-20.log /Volumes/Xin/work/logs/even-logfile-2024-11-20.log
prod-copy-log-three:
	#scp -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>:~/logfile-2024-11-20.log /Volumes/Xin/work/logs/three-logfile-2024-11-20-2.log
	scp -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>:~/logfile-2024-11-21.log /Volumes/Xin/work/logs/three-logfile-2024-11-21.log
prod-copy-log-four:
	scp -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>:~/logfile-2024-11-20.log /Volumes/Xin/work/logs/four-logfile-2024-11-20.log

build:
	/Applications/Unity/Hub/Editor/2021.3.15f1/Unity.app/Contents/MacOS/Unity -projectPath . -batchmode -buildTarget ios -quit -executeMethod SharedUnityBuild.Build.BuildiOSDev -username '<EMAIL>' -password 'P0nd0Un!ty'


ssh-prod-nkmad-realtime:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmad_prod.pem" <EMAIL>
