#!/bin/bash
# https://docs.aws.amazon.com/AmazonECS/latest/developerguide/docker-basics.html
echo "============ Setup Docker Server"
sudo yum update -y

echo "===== start to setup aws"
sudo yum install git -y
echo "===== start to install agent"
sudo yum install amazon-cloudwatch-agent -y
#sudo apt install awscli -y
aws configure set aws_access_key_id "${aws_access_key}"
aws configure set aws_secret_access_key "${aws_secret_key}"

echo ""
echo "===== whoami"
whoami
echo ""

echo ""
echo "===== start to setup docker server"
sudo amazon-linux-extras install docker
sudo service docker start
sudo usermod -a -G docker ec2-user

# install docker compose v1
#sudo curl -L "https://github.com/docker/compose/releases/download/1.26.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
#sudo chmod +x /usr/local/bin/docker-compose
#docker-compose --version
# install docker compose v2
# https://docs.docker.com/compose/install/linux/#install-the-plugin-manually
DOCKER_CONFIG=${DOCKER_CONFIG:-$HOME/.docker}
mkdir -p "$DOCKER_CONFIG"/cli-plugins
curl -SL https://github.com/docker/compose/releases/download/v2.23.3/docker-compose-linux-x86_64 -o "$DOCKER_CONFIG"/cli-plugins/docker-compose
chmod +x "$DOCKER_CONFIG"/cli-plugins/docker-compose
chmod +x /usr/local/lib/docker/cli-plugins/docker-compose
docker compose version
# expect result:  `Docker Compose version v2.23.3`
ls -halt
echo ""

echo ""
echo "===== System Check"
echo ">>>>> start to check the Linux system info"
uname -ra
cat /etc/*-release
ps -aux

echo ">>>>> start to check aws"
ls ~/.aws/credentials
cat ~/.bashrc
aws s3 ls

echo ""
echo ""
echo "===== Setup environment variables"
echo ">>>>> start to setup environments"
{
echo "export COCKROACH_DB_CLOUD_DEV_USER=${COCKROACH_DB_CLOUD_DEV_USER}"
echo "export COCKROACH_DB_CLOUD_DEV_PWD=${COCKROACH_DB_CLOUD_DEV_PWD}"
echo "export COCKROACH_DB_CLOUD_DEV_ADDRESS=${COCKROACH_DB_CLOUD_DEV_ADDRESS}"
echo "export COCKROACH_DB_CLOUD_PROD_USER=${COCKROACH_DB_CLOUD_PROD_USER}"
echo "export COCKROACH_DB_CLOUD_PROD_PWD=${COCKROACH_DB_CLOUD_PROD_PWD}"
echo "export COCKROACH_DB_CLOUD_PROD_ADDRESS=${COCKROACH_DB_CLOUD_PROD_ADDRESS}"
echo "export RDS_POSTGRESQL_PROD_USER=${RDS_POSTGRESQL_PROD_USER}"
echo "export RDS_POSTGRESQL_PROD_PWD=${RDS_POSTGRESQL_PROD_PWD}"
echo "export RDS_POSTGRESQL_PROD_ADDRESS=${RDS_POSTGRESQL_PROD_ADDRESS}"
echo "export LOGGLY_SUBDOMAIN=${LOGGLY_SUBDOMAIN}"
echo "export LOGGLY_USERNAME=${LOGGLY_USERNAME}"
echo "export LOGGLY_TOKEN=${LOGGLY_TOKEN}"
echo "export LOGGLY_PWD=${LOGGLY_PWD}"
} >> ~/.bashrc
# Immediately apply the configuration
source ~/.bashrc  # or source ~/.bash_profile
echo "$COCKROACH_DB_CLOUD_DEV_USER"
echo "$COCKROACH_DB_CLOUD_PROD_USER"
cat ~/.bashrc

echo ""
echo ""
echo "===== Setup GitHub Docker Registry"
{
echo "export CR_PAT=${CR_PAT}"
} >> ~/.bashrc
source ~/.bashrc  # or source ~/.bash_profile
cat ~/.bashrc
whoami
#sudo docker login ghcr.io -u xinatcg -p "$CR_PAT"
#su ec2-user -c "docker login ghcr.io -u xinatcg -p $CR_PAT"
touch ~/docker-login.sh
chmod +x ~/docker-login.sh
{
echo "docker login ghcr.io -u xinatcg -p $CR_PAT"
} >> ~/docker-login.sh
cat ~/docker-login.sh
ls -halt
#echo "$CR_PAT" | docker login ghcr.io -u xinatcg --password-stdin
#docker login ghcr.io
#cat ~/.docker/config.json
echo "=====start to setup the loggly"
echo "=====start to setup syslog"
# modify rsyslog config，enable UDP option
# 修改 rsyslog 配置文件，启用 UDP 支持
sudo sed -i 's/#module(load="imudp")/module(load="imudp")/' /etc/rsyslog.conf
sudo sed -i 's/#input(type="imudp" port="514")/input(type="imudp" port="514")/' /etc/rsyslog.conf

# restart rsyslog service
# 重启 rsyslog 服务
sudo systemctl restart rsyslog
# check rsyslog service status
# 检查 rsyslog 服务状态
sudo systemctl status rsyslog

echo "===== yum install -y rsyslog-gnutls start"
# install rsyslog-gnutls
sudo yum install -y rsyslog-gnutls
echo "===== yum install -y rsyslog-gnutls end"

echo "LOGGLY_SUBDOMAIN : ${LOGGLY_SUBDOMAIN}"
echo "LOGGLY_USERNAME : ${LOGGLY_USERNAME}"
echo "LOGGLY_TOKEN : ${LOGGLY_TOKEN}"
curl -O https://www.loggly.com/install/configure-linux.sh
sudo bash configure-linux.sh -a "${LOGGLY_SUBDOMAIN}" -t "${LOGGLY_TOKEN}" -u "${LOGGLY_USERNAME}" --password "${LOGGLY_PWD}"
curl -O https://www.loggly.com/install/configure-file-monitoring.sh
#sudo bash configure-file-monitoring.sh -a "${LOGGLY_SUBDOMAIN}" -t "${LOGGLY_TOKEN}" -u "${LOGGLY_USERNAME}" --password "${LOGGLY_PWD}" -f /home/<USER>/data/logfile.log -l nkmfd