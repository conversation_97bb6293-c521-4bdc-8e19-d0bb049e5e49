# 最新的 Packer 的使用总结

1. 切换为 HCL 格式, 并且使用 Ubuntu 做 base image, 查看该文件: [ubuntu-docker.pkr.hcl](ubuntu-docker.pkr.hcl)
2. Packer 使用 1.13.1 [ami-ubuntu-docker-prod-nkmad.yml](../.github/workflows/ami-ubuntu-docker-prod-nkmad.yml)
3. 需要配置插件 [versions.pkr.hcl](versions.pkr.hcl)
4. 使用新的 setup bash shell: [ubuntu-docker-setup.sh](ubuntu-docker-setup.sh)
5. 安装 1Panel, 手动下载, 并且修改安装脚本支持获取环境变量来控制安装的参数 [1panel_v2_quick_start.sh](1panel_v2_quick_start.sh) 和这个 [install.sh](1panel-v2.0.4-linux-amd64/install.sh)
6. cloud watch config 升级, 支持 App metric, 并聚合. [aws_cw_agent_config_nkmad.json](aws_cw_agent_config_nkmad.json)

# 如何查找 AIM

https://cloud-images.ubuntu.com/locator/ec2/ 搜 id
https://amilookup.com/ 通过 id 搜索详细信息

比如 Canonical, Ubuntu, 24.04, amd64 noble image

```text
VirtualizationType
hvm
Description
Canonical, Ubuntu, 24.04, amd64 noble image
Hypervisor
xen
ImageOwnerAlias
amazon
EnaSupport
true
SriovNetSupport
simple
ImageId
**ami-020cba7c55df1f615**
State
available
BlockDeviceMappings
{ "DeviceName": "/dev/sda1", "Ebs": { "DeleteOnTermination": true, "SnapshotId": "snap-0bc1d350c2ac74766", "VolumeSize": 8, "VolumeType": "gp3", "Encrypted": false } }
Architecture
x86_64
ImageLocation
amazon/ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-20250610
RootDeviceType
ebs
OwnerId
099720109477
RootDeviceName
/dev/sda1
CreationDate
2025-06-10T10:42:58.000Z
Public
true
ImageType
machine
Name
ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-20250610
```


```bash
aws ec2 describe-images \
  --owners 099720109477 \
  --filters "Name=name,Values=ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-*" \
            "Name=virtualization-type,Values=hvm" \
            "Name=root-device-type,Values=ebs" \
  --query 'Images[*].{Name:Name,CreationDate:CreationDate}' \
  --region us-east-1 | sort -r -k2
```

# AMI List



## ami-amz-docker-live

该 AMI 用于 nakama prod 的 docker 环境.

1. docker compose
2. aws credential
3. other secrets: database connection, admin password
4. 



# Packer 构建过程

我们通过版本控制来触发 github action 进行 packer build

1. 首先我们需要准备好 packer 相关配置, GitHub action workflow 以及版本自动化脚本
2. 每次更新的 packer 内容, 通过自动脚本 release-ami-xxx.sh 触发对应的 packer 并打 tag
3. 对应的 tag 会触发对应的 packer GitHub action workflow
4. 整个过程重要的参数: [NAME: ami-amz-docker-live](../.github/workflows/ami-amazon-docker-live.yml)

# AWS Monitor

```bash
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a start
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a stop

sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -s -c file:/home/<USER>/aws_cloudwatch_agent_config_instance.json
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a status -m ec2 -s -c file:/home/<USER>/aws_cloudwatch_agent_config_instance.json

```

还需要权限, 目前使用 Cityboom 的 EC2 Role