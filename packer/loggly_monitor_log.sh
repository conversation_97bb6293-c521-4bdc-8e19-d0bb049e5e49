#!/bin/bash
# loggly_monitor_log
# Set the user
USER="ec2-user"

# Check if node_name is provided as a script parameter
if [ "$#" -eq 1 ]; then
    node_name="$1"
else
    # If not provided, set a default value or handle the situation accordingly
    node_name="default"
fi

curl -O https://www.loggly.com/install/configure-file-monitoring.sh
sudo bash configure-file-monitoring.sh -a "${LOGGLY_SUBDOMAIN}" -t "${LOGGLY_TOKEN}" -u "${LOGGLY_USERNAME}" --password "${LOGGLY_PWD}" -f /home/<USER>/data/logfile.log -l ${node_name}