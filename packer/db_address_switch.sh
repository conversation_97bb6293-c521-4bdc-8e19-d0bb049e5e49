#!/bin/bash

# Set the user
USER="ec2-user"

# Set the environment variables for RDS and CockroachDB addresses
RDS_POSTGRESQL_PROD_ADDRESS="${RDS_POSTGRESQL_PROD_ADDRESS}"
COCKROACH_DB_CLOUD_PROD_ADDRESS="${COCKROACH_DB_CLOUD_PROD_ADDRESS}"

# Check if use_rds_db is provided as a script parameter
if [ "$#" -eq 1 ]; then
    use_rds_db="$1"
else
    # If not provided, set a default value or handle the situation accordingly
    use_rds_db="0"
fi

# Check the value of use_rds_db
if [ "${use_rds_db}" == "1" ]; then
    # If use_rds_db is 1 (true), use the RDS address
    export DB_ADDRESS="${RDS_POSTGRESQL_PROD_ADDRESS}"
else
    # Otherwise, use the CockroachDB address
    export DB_ADDRESS="${COCKROACH_DB_CLOUD_PROD_ADDRESS}"
fi

# Write the configuration to the user's environment file
echo "export DB_ADDRESS=${DB_ADDRESS}" >> "/home/<USER>/.bashrc"

# Print a message
echo "DB_ADDRESS has been configured as: ${DB_ADDRESS}"

## Usage

#./db_address_switch.sh true
#./db_address_switch.sh false