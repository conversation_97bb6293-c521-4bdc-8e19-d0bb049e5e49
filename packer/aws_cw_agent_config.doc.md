
https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-Agent-Configuration-File-Details.html



append_dimensions – Optional. Adds Amazon EC2 metric dimensions to all metrics collected by the agent. This also causes the agent to not publish the hostname as a dimension.

The only supported key-value pairs for append_dimensions are shown in the following list. Any other key-value pairs are ignored. The agent supports these key-value pairs exactly as they are shown in the following list. You can't change the key values to publish different dimension names for them.

"ImageId":"${aws:ImageId}" sets the instance's AMI ID as the value of the ImageId dimension.

"InstanceId":"${aws:InstanceId}" sets the instance's instance ID as the value of the InstanceId dimension.

"InstanceType":"${aws:InstanceType}" sets the instance's instance type as the value of the InstanceType dimension.

"AutoScalingGroupName":"${aws:AutoScalingGroupName}" sets the instance's Auto Scaling group name as the value of the AutoScalingGroupName dimension.

If you want to append dimensions to metrics with arbitrary key-value pairs, use the append_dimensions parameter in the field for that particular type of metric.

If you specify a value that depends on Amazon EC2 metadata and you use proxies, you must make sure that the server can access the endpoint for Amazon EC2. For more information about these endpoints, see Amazon Elastic Compute Cloud (Amazon EC2) in the Amazon Web Services General Reference.