{"agent": {"metrics_collection_interval": 60, "run_as_user": "root"}, "metrics": {"append_dimensions": {"AutoScalingGroupName": "${aws:AutoScalingGroupName}", "ImageId": "${aws:ImageId}", "InstanceId": "${aws:InstanceId}", "InstanceType": "${aws:InstanceType}"}, "aggregation_dimensions": [["AutoScalingGroupName"]], "metrics_collected": {"disk": {"measurement": ["used_percent"], "metrics_collection_interval": 60, "resources": ["/"]}, "mem": {"measurement": ["mem_used_percent"], "metrics_collection_interval": 60}}}}