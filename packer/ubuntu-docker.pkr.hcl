# Packer HCL2 Configuration for Ubuntu Docker Image
# This configuration is compatible with Packer 1.7.0 and above (including 1.13.1+)

# Define variables that can be passed via environment variables or -var flags
variable "AWS_ACCESS_KEY_ID" {
  type        = string
  default     = env("AWS_ACCESS_KEY_ID")
  description = "AWS Access Key ID"
}

variable "AWS_SECRET_ACCESS_KEY" {
  type        = string
  default     = env("AWS_SECRET_ACCESS_KEY")
  description = "AWS Secret Access Key"
}

variable "AWS_DEFAULT_REGION" {
  type        = string
  default     = env("AWS_DEFAULT_REGION")
  description = "AWS Region for AMI creation"
}

variable "NAME" {
  type        = string
  default     = env("NAME")
  description = "Base name for the AMI"
}

variable "APP" {
  type        = string
  default     = env("APP")
  description = "APP name"
}

variable "LOGGLY_SUBDOMAIN" {
  type        = string
  default     = env("LOGGLY_SUBDOMAIN")
  description = "Loggly subdomain"
}

variable "LOGGLY_TOKEN" {
  type        = string
  default     = env("LOGGLY_TOKEN")
  description = "Loggly token"
}

variable "LOGGLY_USERNAME" {
  type        = string
  default     = env("LOGGLY_USERNAME")
  description = "Loggly username"
}

variable "LOGGLY_PWD" {
  type        = string
  default     = env("LOGGLY_PWD")
  description = "Loggly password"
}

variable "CR_PAT" {
  type        = string
  default     = env("CR_PAT")
  description = "GitHub Container Registry Personal Access Token"
  sensitive   = true # Mark as sensitive to prevent logging its value
}

variable "NAME_SUFFIX" {
  type        = string
  default     = env("NAME_SUFFIX")
  description = "Suffix for the AMI name (e.g., dev, prod)"
}

variable "GIT_TAG" {
  type        = string
  default     = env("GIT_TAG")
  description = "Git tag associated with the build"
}

# 移除了 version_file_name 变量，因为其默认值不能引用其他变量。
# 其路径现在直接在 provisioner 中构建。

variable "COCKROACH_DB_CLOUD_DEV_USER" {
  type        = string
  default     = env("COCKROACH_DB_CLOUD_DEV_USER")
  description = "CockroachDB Cloud Development User"
}

variable "COCKROACH_DB_CLOUD_DEV_PWD" {
  type        = string
  default     = env("COCKROACH_DB_CLOUD_DEV_PWD")
  description = "CockroachDB Cloud Development Password"
  sensitive   = true
}

variable "COCKROACH_DB_CLOUD_DEV_ADDRESS" {
  type        = string
  default     = env("COCKROACH_DB_CLOUD_DEV_ADDRESS")
  description = "CockroachDB Cloud Development Address"
}

variable "COCKROACH_DB_CLOUD_PROD_USER" {
  type        = string
  default     = env("COCKROACH_DB_CLOUD_PROD_USER")
  description = "CockroachDB Cloud Production User"
}

variable "COCKROACH_DB_CLOUD_PROD_PWD" {
  type        = string
  default     = env("COCKROACH_DB_CLOUD_PROD_PWD")
  description = "CockroachDB Cloud Production Password"
  sensitive   = true
}

variable "COCKROACH_DB_CLOUD_PROD_ADDRESS" {
  type        = string
  default     = env("COCKROACH_DB_CLOUD_PROD_ADDRESS")
  description = "CockroachDB Cloud Production Address"
}

variable "PANEL_LANG_CHOICE" {
  type        = string
  default     = env("PANEL_LANG_CHOICE")
  description = "1Panel Language Choice"
}

variable "PANEL_PORT" {
  type        = string
  default     = env("PANEL_PORT")
  description = "1Panel PANEL_PORT"
}

variable "PANEL_BASE_DIR" {
  type        = string
  default     = env("PANEL_BASE_DIR")
  description = "1Panel PANEL_BASE_DIR"
}

variable "PANEL_ENTRANCE" {
  type        = string
  default     = env("PANEL_ENTRANCE")
  description = "1Panel PANEL_ENTRANCE"
}

variable "PANEL_USERNAME" {
  type        = string
  default     = env("PANEL_USERNAME")
  description = "1Panel PANEL_USERNAME"
}

variable "PANEL_PASSWORD" {
  type        = string
  default     = env("PANEL_PASSWORD")
  description = "1Panel PANEL_PASSWORD"
}


# Define the source (builder) for the AMI
source "amazon-ebs" "ubuntu-docker-latest" {
  access_key = var.AWS_ACCESS_KEY_ID
  ami_name   = "ubuntu-docker-server-${var.NAME_SUFFIX}-latest"
  instance_type = "t3.medium"
  region        = var.AWS_DEFAULT_REGION
  secret_key    = var.AWS_SECRET_ACCESS_KEY
  ssh_username  = "ubuntu" # Default SSH user for Ubuntu AMIs
  force_deregister = true # Keep this one
  force_delete_snapshot = true # Keep this one

  # Filter to find the latest Ubuntu 24.04 LTS AMI
  source_ami_filter {
    filters = {
      virtualization-type = "hvm"
      name                = "ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-*"
      root-device-type    = "ebs"
    }
    owners = ["************"] # Canonical's AWS account ID for Ubuntu AMIs
    most_recent = true
  }

  tags = {
    App    = var.NAME_SUFFIX
    OS_Version    = "Ubuntu 24.04 LTS"
    Release       = "Latest"
    Git_Tag       = var.GIT_TAG
    Base_AMI_Name = "{{ .SourceAMIName }}"
    Extra         = "{{ .SourceAMITags.TagName }}"
  }
}

source "amazon-ebs" "ubuntu-docker-versioned" {
  access_key = var.AWS_ACCESS_KEY_ID
  ami_name   = "ubuntu-docker-server-${var.NAME_SUFFIX}-${split("-", var.GIT_TAG)[0]}"
  instance_type = "t3.medium"
  region        = var.AWS_DEFAULT_REGION
  secret_key    = var.AWS_SECRET_ACCESS_KEY
  ssh_username  = "ubuntu" # Default SSH user for Ubuntu AMIs
  force_deregister = true # Keep this one
  force_delete_snapshot = true # Keep this one

  # Filter to find the latest Ubuntu 24.04 LTS AMI
  source_ami_filter {
    filters = {
      virtualization-type = "hvm"
      name                = "ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-*"
      root-device-type    = "ebs"
    }
    owners = ["************"] # Canonical's AWS account ID for Ubuntu AMIs
    most_recent = true
  }

  tags = {
    App    = var.NAME_SUFFIX
    OS_Version    = "Ubuntu 24.04 LTS"
    Release       = "${split("-", var.GIT_TAG)[0]}"
    Git_Tag       = var.GIT_TAG
    Base_AMI_Name = "{{ .SourceAMIName }}"
    Extra         = "{{ .SourceAMITags.TagName }}"
  }
}

# Define the build process
build {
  sources = [
    "source.amazon-ebs.ubuntu-docker-latest",
    "source.amazon-ebs.ubuntu-docker-versioned"
  ]

  provisioner "file" {
    source      = "./packer/packer.md"
    destination = "/home/<USER>/"
  }

  provisioner "file" {
    # 直接在这里构建 version 文件的路径，使用 var.NAME
    source      = "./packer/version-${var.NAME}.txt"
    destination = "/home/<USER>/"
  }

  # Provisioner to upload the 1Panel zip file
  provisioner "file" {
    source      = "./packer/1panel-v2.0.4-linux-amd64-modified.zip"
    destination = "/tmp/1panel-v2.0.4-linux-amd64-modified.zip"
  }

  # Provisioner to upload the AWS CloudWatch Agent config file
  # 这里我们使用最新的 cw 配置
  provisioner "file" {
    source      = "./packer/aws_cw_agent_config_${var.APP}.json"
    destination = "/tmp/aws_cloudwatch_agent_config.json"
  }

  provisioner "file" {
    source      = "./packer/db_address_switch_ubuntu.sh"
    destination = "/home/<USER>/"
  }

  provisioner "file" {
    source      = "./packer/loggly_monitor_log_ubuntu.sh"
    destination = "/home/<USER>/"
  }

  # Provisioner to execute the setup script
  provisioner "shell" {
    script = "./packer/ubuntu-docker-setup.sh"
    environment_vars = [
      "NAME=${var.NAME}",
      "NAME_SUFFIX=${var.NAME_SUFFIX}",
      "APP=${var.APP}",
      "GIT_TAG=${var.GIT_TAG}",
      # CockroachDB
      "COCKROACH_DB_CLOUD_DEV_USER=${var.COCKROACH_DB_CLOUD_DEV_USER}",
      "COCKROACH_DB_CLOUD_DEV_PWD=${var.COCKROACH_DB_CLOUD_DEV_PWD}",
      "COCKROACH_DB_CLOUD_DEV_ADDRESS=${var.COCKROACH_DB_CLOUD_DEV_ADDRESS}",
      "COCKROACH_DB_CLOUD_PROD_USER=${var.COCKROACH_DB_CLOUD_PROD_USER}",
      "COCKROACH_DB_CLOUD_PROD_PWD=${var.COCKROACH_DB_CLOUD_PROD_PWD}",
      "COCKROACH_DB_CLOUD_PROD_ADDRESS=${var.COCKROACH_DB_CLOUD_PROD_ADDRESS}",
      # Loggly
      "LOGGLY_SUBDOMAIN=${var.LOGGLY_SUBDOMAIN}",
      "LOGGLY_TOKEN=${var.LOGGLY_TOKEN}",
      "LOGGLY_USERNAME=${var.LOGGLY_USERNAME}",
      "LOGGLY_PWD=${var.LOGGLY_PWD}",
      # 1Panel
      "PANEL_LANG_CHOICE=${var.PANEL_LANG_CHOICE}",
      "PANEL_PORT=${var.PANEL_PORT}",
      "PANEL_BASE_DIR=${var.PANEL_BASE_DIR}",
      "PANEL_ENTRANCE=${var.PANEL_ENTRANCE}",
      "PANEL_USERNAME=${var.PANEL_USERNAME}",
      "PANEL_PASSWORD=${var.PANEL_PASSWORD}",
      # GitHub PAT
      "CR_PAT=${var.CR_PAT}",
      # AWS
      "AWS_ACCESS_KEY_ID=${var.AWS_ACCESS_KEY_ID}",
      "AWS_SECRET_ACCESS_KEY=${var.AWS_SECRET_ACCESS_KEY}",
      "AWS_DEFAULT_REGION=${var.AWS_DEFAULT_REGION}",
    ]
  }
}
