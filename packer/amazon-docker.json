{"variables": {"aws_access_key": "{{env `AWS_ACCESS_KEY_ID`}}", "aws_secret_key": "{{env `AWS_SECRET_ACCESS_KEY`}}", "REGION": "{{env `REGION`}}", "NAME": "{{env `NAME`}}", "INSTALL_REDIS": "{{env `INSTALL_REDIS`}}", "COCKROACH_DB_CLOUD_DEV_USER": "{{env `COCKROACH_DB_CLOUD_DEV_USER`}}", "COCKROACH_DB_CLOUD_DEV_PWD": "{{env `COCKROACH_DB_CLOUD_DEV_PWD`}}", "COCKROACH_DB_CLOUD_DEV_ADDRESS": "{{env `COCKROACH_DB_CLOUD_DEV_ADDRESS`}}", "COCKROACH_DB_CLOUD_PROD_USER": "{{env `COCKROACH_DB_CLOUD_PROD_USER`}}", "COCKROACH_DB_CLOUD_PROD_PWD": "{{env `COCKROACH_DB_CLOUD_PROD_PWD`}}", "COCKROACH_DB_CLOUD_PROD_ADDRESS": "{{env `COCKROACH_DB_CLOUD_PROD_ADDRESS`}}", "RDS_POSTGRESQL_PROD_USER": "{{env `RDS_POSTGRESQL_PROD_USER`}}", "RDS_POSTGRESQL_PROD_PWD": "{{env `RDS_POSTGRESQL_PROD_PWD`}}", "RDS_POSTGRESQL_PROD_ADDRESS": "{{env `RDS_POSTGRESQL_PROD_ADDRESS`}}", "LOGGLY_SUBDOMAIN": "{{env `LOGGLY_SUBDOMAIN`}}", "LOGGLY_TOKEN": "{{env `LOGGLY_TOKEN`}}", "LOGGLY_USERNAME": "{{env `LOGGLY_USERNAME`}}", "LOGGLY_PWD": "{{env `LOGGLY_PWD`}}", "GIT_TAG": "{{env `GIT_TAG`}}", "CR_PAT": "{{env `CR_PAT`}}", "NAME_SUFFIX": "{{env `NAME_SUFFIX`}}", "version_file_name": "./packer/version-{{user `NAME`}}.txt"}, "builders": [{"access_key": "{{user `aws_access_key`}}", "ami_name": "amazon-docker-server-nakama-{{user `NAME_SUFFIX`}}", "instance_type": "t3.micro", "region": "{{user `REGION`}}", "secret_key": "{{user `aws_secret_key`}}", "source_ami_filter": {"filters": {"virtualization-type": "hvm", "name": "amzn2-ami-hvm-2.0.????????.?-x86_64-gp2", "root-device-type": "ebs"}, "owners": ["amazon"], "most_recent": true}, "ssh_username": "ec2-user", "type": "amazon-ebs", "force_deregister": true, "force_delete_snapshot": true, "tags": {"OS_Version": "Ubuntu", "Release": "Latest", "Git_Tag": "{{user `GIT_TAG`}}", "Base_AMI_Name": "{{ .SourceAM<PERSON>ame }}", "Extra": "{{ .SourceAMITags.TagName }}"}}], "provisioners": [{"type": "file", "source": "./packer/packer.md", "destination": "/home/<USER>/"}, {"type": "file", "source": "{{user `version_file_name`}}", "destination": "/home/<USER>/"}, {"type": "file", "source": "./packer/aws_cloudwatch_agent_config.json", "destination": "/home/<USER>/"}, {"type": "file", "source": "./packer/db_address_switch.sh", "destination": "/home/<USER>/"}, {"type": "file", "source": "./packer/loggly_monitor_log.sh", "destination": "/home/<USER>/"}, {"type": "shell", "inline": ["ls -al /home/<USER>", "cat /home/<USER>/packer.md"]}, {"type": "shell", "script": "./packer/amazon-docker-setup.sh", "environment_vars": ["COCKROACH_DB_CLOUD_DEV_USER={{user `COCKROACH_DB_CLOUD_DEV_USER`}}", "COCKROACH_DB_CLOUD_DEV_PWD={{user `COCKROACH_DB_CLOUD_DEV_PWD`}}", "COCKROACH_DB_CLOUD_DEV_ADDRESS={{user `COCKROACH_DB_CLOUD_DEV_ADDRESS`}}", "COCKROACH_DB_CLOUD_PROD_USER={{user `COCKROACH_DB_CLOUD_PROD_USER`}}", "COCKROACH_DB_CLOUD_PROD_PWD={{user `COCKROACH_DB_CLOUD_PROD_PWD`}}", "COCKROACH_DB_CLOUD_PROD_ADDRESS={{user `COCKROACH_DB_CLOUD_PROD_ADDRESS`}}", "RDS_POSTGRESQL_PROD_USER={{user `RDS_POSTGRESQL_PROD_USER`}}", "RDS_POSTGRESQL_PROD_PWD={{user `RDS_POSTGRESQL_PROD_PWD`}}", "RDS_POSTGRESQL_PROD_ADDRESS={{user `RDS_POSTGRESQL_PROD_ADDRESS`}}", "LOGGLY_SUBDOMAIN={{user `LOGGLY_SUBDOMAIN`}}", "LOGGLY_TOKEN={{user `LOGGLY_TOKEN`}}", "LOGGLY_USERNAME={{user `LOGGLY_USERNAME`}}", "LOGGLY_PWD={{user `LOGGLY_PWD`}}", "CR_PAT={{user `CR_PAT`}}", "aws_access_key={{user `aws_access_key`}}", "aws_secret_key={{user `aws_secret_key`}}"]}]}