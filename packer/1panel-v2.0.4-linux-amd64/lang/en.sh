#!/bin/bash

TXT_START_INSTALLATION="======================= Starting Installation ======================="
TXT_RUN_AS_ROOT="Please run this script as root or with sudo permissions"
TXT_SUCCESS_MESSAGE="Success"
TXT_SUCCESSFULLY_MESSAGE="Successfully"
TXT_FAIELD_MESSAGE="Failed"
TXT_IGNORE_MESSAGE="Ignore"
TXT_SKIP_MESSAGE="Skip"
TXT_PANEL_ALREADY_INSTALLED="1Panel Linux server management panel is already installed, please do not install again"
TXT_SET_INSTALL_DIR="Set 1Panel installation directory (default is /opt): "
TXT_PROVIDE_FULL_PATH="Please provide the full path of the directory"
TXT_SELECTED_INSTALL_PATH="The installation path you selected is"
TXT_TIMEOUT_USE_DEFAULT_PATH="(Timeout set, using default installation path /opt)"
TXT_CREATE_NEW_CONFIG="Creating new configuration file"
TXT_ACCELERATION_CONFIG_ADDED="Image acceleration configuration has been added."
TXT_USING_TENCENT_MIRROR="Tencent Cloud intranet image acceleration configuration added."
TXT_ACCELERATION_CONFIG_NOT="Image acceleration is not configured."
TXT_ACCELERATION_CONFIG_ADD="Do you want to configure image acceleration(y/n): "
TXT_ACCELERATION_CONFIG_EXISTS="The configuration file already exists, we will backup the existing configuration file to: "
TXT_RESTARTING_DOCKER="Restarting Docker service..."
TXT_DOCKER_RESTARTED="Docker service successfully restarted."
TXT_INSTALL_DOCKER_CONFIRM="Docker is not installed. Do you want to install it? [y/n]: "
TXT_CANCEL_INSTALL_DOCKER="Docker installation cancelled"
TXT_INVALID_YN_INPUT="nvalid input. Please enter y or n"
TXT_DOCKER_INSTALL_ONLINE="... Installing Docker online"
TXT_ACCELERATOR_NOT_CONFIGURED="Image acceleration not configured."
TXT_LOW_DOCKER_VERSION="Detected that the server’s Docker version is below 20.x. It is recommended to upgrade manually to avoid functionality limitations."
TXT_INSTALL_DOCKER_ONLINE="... Installing Docker online"
TXT_DOWNLOAD_DOCKER_SCRIPT_FAIL="Failed to download installation script from"
TXT_DOWNLOAD_DOCKER_SCRIPT="downloading docker installation script"
TXT_DOWNLOAD_DOCKER_SCRIPT_SUCCESS="Downloaded docker from"
TXT_TRY_NEXT_LINK="trying the next alternative link"
TXT_DOWNLOAD_FAIELD="Failed to download the installation script from"
TXT_ALL_DOWNLOAD_ATTEMPTS_FAILED="All download attempts failed. You may try installing Docker manually by running the following command:"
TXT_REGIONS_OTHER_THAN_CHINA="No need to change the source"
TXT_DOCKER_INSTALL_SUCCESS="Docker installed successfully"
TXT_DOCKER_INSTALL_FAIL="Docker installation failed\nYou may try installing Docker using an offline package, refer to the following link for detailed installation steps: https://docs.1panel.hk/installation/"
TXT_CHOOSE_LOWEST_LATENCY_SOURCE="Choose the source with the lowest latency"
TXT_CHOOSE_LOWEST_LATENCY_DELAY="Delay (in seconds)"
TXT_CANNOT_SELECT_SOURCE="Unable to select source for installation"
TXT_DOCKER_START_NOTICE="... start docker"
TXT_DOCKER_MAY_EFFECT_STORE="which may affect the normal use of the App Store."
TXT_SET_PANEL_PORT="Set 1Panel port (default is"
TXT_INPUT_PORT_NUMBER="Error: The entered port number must be between 1 and 65535"
TXT_THE_PORT_U_SET="The port you set is: "
TXT_PORT_OCCUPIED="Port is occupied, please re-enter..."
TXT_FIREWALL_OPEN_PORT="Opening firewall port"
TXT_FIREWALL_NOT_ACTIVE_SKIP="Firewall is not active, skipping port opening"
TXT_FIREWALL_NOT_ACTIVE_IGNORE="Firewall is not active, ignoring port opening"
TXT_SET_PANEL_ENTRANCE="Set 1Panel secure entrance (default is"
TXT_INPUT_ENTRANCE_RULE="Error: Panel secure entrance only supports letters, numbers, underscores, length 3-30 characters"
TXT_YOUR_PANEL_ENTRANCE="The panel secure entrance you set is"
TXT_SET_PANEL_USER="Set 1Panel panel user (default is"
TXT_INPUT_USERNAME_RULE="Error: Panel user only supports letters, numbers, underscores, length 3-30 characters"
TXT_YOUR_PANEL_USERNAME="The panel user you set is"
TXT_SET_PANEL_PASSWORD="Set 1Panel panel password, press Enter to continue after setting (default is"
TXT_INPUT_PASSWORD_RULE="Error: Panel password only supports letters, numbers, special characters (!@#$%*_,.?), length 8-30 characters"
TXT_CONFIGURE_PANEL_SERVICE="Configuring 1Panel Service"
TXT_START_PANEL_SERVICE="Starting 1Panel service"
TXT_PANEL_SERVICE_START_SUCCESS="1Panel service has started successfully. Continuing with post-setup tasks, please wait..."
TXT_PANEL_SERVICE_START_ERROR="Error starting 1Panel service!"
TXT_THANK_YOU_WAITING="=================Thank you for your patience, installation is complete=================="
TXT_BROWSER_ACCESS_PANEL="Please access the panel using your browser:"
TXT_EXTERNAL_ADDRESS="External address:"
TXT_INTERNAL_ADDRESS="Internal address:"
TXT_PANEL_USER="Panel user:"
TXT_PANEL_PASSWORD="Panel password:"
TXT_PROJECT_OFFICIAL_WEBSITE="Official website: https://1panel.hk"
TXT_PROJECT_DOCUMENTATION="Project documentation: https://docs.1panel.hk"
TXT_PROJECT_REPOSITORY="Code repository: https://github.com/1Panel-dev/1Panel"
TXT_COMMUNITY="Join the 1Panel community on Discord for support and discussions: https://discord.gg/bUpUqWqdRr"
TXT_OPEN_PORT_SECURITY_GROUP="If you are using a cloud server, please open the port in the security group"
TXT_REMEMBER_YOUR_PASSWORD="For your server security, you will not be able to see your password again after leaving this screen, please remember your password."
TXT_PANEL_SERVICE_STATUS="Check 1Panel service status"
TXT_PANEL_SERVICE_RESTART="Restart 1Panel service"
TXT_PANEL_SERVICE_STOP="Stop 1Panel service"
TXT_PANEL_SERVICE_START="Start 1Panel service"
TXT_PANEL_SERVICE_UNINSTALL="Uninstall 1Panel service"
TXT_PANEL_SERVICE_USER_INFO="Get 1Panel user information"
TXT_PANEL_SERVICE_LISTEN_IP="Switch 1Panel listening IP"
TXT_PANEL_SERVICE_VERSION="Get 1Panel version information"
TXT_PANEL_SERVICE_UPDATE="Update 1Panel system"
TXT_PANEL_SERVICE_RESET="Reset 1Panel system"
TXT_PANEL_SERVICE_RESTORE="Restore 1Panel system"
TXT_PANEL_SERVICE_UNINSTALL_NOTICE="Uninstallation will stop and remove the 1Panel service. Continue? [y/n]: "
TXT_PANEL_SERVICE_UNINSTALL_START="Starting to uninstall 1Panel Linux Server Management Panel"
TXT_PANEL_SERVICE_UNINSTALL_STOP="Stopping 1Panel service processes"
TXT_PANEL_SERVICE_UNINSTALL_REMOVE="Removing 1Panel executables and service-related configurations"
TXT_PANEL_SERVICE_UNINSTALL_REMOVE_CONFIG="Reloading service configuration files"
TXT_PANEL_SERVICE_UNINSTALL_REMOVE_SUCCESS="Uninstallation completed!"
TXT_PANEL_DATA_KEEP_PROMPT="Delete 1Panel data directory? [y/n]: "
TXT_PANEL_DATA_KEEP="Keep 1Panel data directory"
TXT_PANEL_DATA_DELETE="Delete 1Panel data directory"
TXT_PANEL_SERVICE_RESTORE_NOTICE="1Panel will be restored to the last stable version. Do you want to continue? [y/n]: "
TXT_PANEL_SERVICE_UNSUPPORTED_PARAMETER="Unsupported parameters, please use help or --help parameter to get help"
TXT_PANEL_CONTROL_SCRIPT="1Panel control script"
TXT_LANG_SELECTED_MSG="Language already selected: "
TXT_LANG_PROMPT_MSG="Select a language:"
TXT_LANG_CHOICE_MSG="Enter the number corresponding to your language choice: "
TXT_LANG_SELECTED_CONFIRM_MSG="You selected: "
TXT_LANG_INVALID_MSG="Invalid selection. Defaulting to English (en)."
TXT_LANG_NOT_FOUND_MSG="Language file not found:"
TXT_PANEL_SERVICE_REQUIRE_CORE_OR_AGENT="Please specify: core or agent"
TXT_PANEL_SERVICE_REQUIRE_CORE_AGENT_OR_ALL="Please specify: core, agent or all"
