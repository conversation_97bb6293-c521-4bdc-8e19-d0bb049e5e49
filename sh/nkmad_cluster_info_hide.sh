#!/usr/bin/env bash

# This script will only upload new configPath in the folder of configPath, all existing configPath in the S3 will not be override.
#   if want to update existing configPath, new version should be updated.
#   if replace the existing configPath in the S3, the existing player who has download the configPath cannot get the update

# support specify environment
# 1. if non-parameter, use `dev` as default
# 2. if `prod` as first parameter, use `prod` environment
# 3. if unknown environment such as xxx, use `dev` as default
# 4. Now also supports an environment variable for ENV, e.g., export APP_ENV=prod

# ENV variable precedence: APP_ENV environment variable > first command-line argument > 'dev' default
ENV=${APP_ENV:-${1:-'dev'}}
DEV_ENV='dev'
PROD_ENV='prod'
LOCAL_ENV='local'

echo "$ENV"

case $ENV in
  "${DEV_ENV}") echo "Env is dev";;
  "${PROD_ENV}") echo "Env is prod.";;
  "${LOCAL_ENV}") echo "Env is local.";;
  *) echo "Wrong env, use dev as default"
    ENV=${PROD_ENV}
    ;;
esac

echo "Finally env: $ENV"

# Predefined API paths
API_GET_VERSION="s2s_get_version"
API_UPDATE_CONFIG="s2s_update_config"
API_UPDATE_CONFIG_NODE="s2s_update_config_node"
API_GET_CONFIG="s2s_get_config"

# 函数：获取 ASG 实例的公有和私有 IP 地址
# 参数：$1 - Auto Scaling Group 名称
get_asg_instance_ips() {
    ASG_NAME="$1"
    INSTANCE_IDS=""
    INSTANCE_IPS=""

    echo "正在获取 Auto Scaling Group '${ASG_NAME}' 中的实例 ID..." >&2 # Redirect to stderr
    INSTANCE_IDS=$(aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names "${ASG_NAME}" \
        --query 'AutoScalingGroups[0].Instances[*].InstanceId' \
        --output text 2>/dev/null) # 忽略错误输出

    if [ -z "${INSTANCE_IDS}" ]; then
        echo "未找到 Auto Scaling Group '${ASG_NAME}' 中的任何实例。" >&2 # Redirect to stderr
        return 1 # 返回非零表示失败
    fi

    echo "Auto Scaling Group '${ASG_NAME}' 中的实例 ID: ${INSTANCE_IDS}" >&2 # Redirect to stderr
    echo "正在获取公有和私有 IP 地址..." >&2 # Redirect to stderr

    # 同时查询公有和私有IP地址
    INSTANCE_IPS=$(aws ec2 describe-instances \
        --instance-ids ${INSTANCE_IDS} \
        --query 'Reservations[*].Instances[*].[PublicIpAddress, PrivateIpAddress]' \
        --output text 2>/dev/null) # 忽略错误输出

    if [ -z "${INSTANCE_IPS}" ]; then
        echo "未找到任何公有或私有 IP 地址。" >&2 # Redirect to stderr
        return 1 # 返回非零表示失败
    fi

    echo "获取到的 IP 地址 (公有\t私有):" >&2 # Redirect to stderr
    echo "${INSTANCE_IPS}" >&2 # Redirect to stderr
    echo "" >&2 # Redirect to stderr

    # 将 IP 地址对返回给调用者 (只输出到 stdout)
    echo "${INSTANCE_IPS}"
    return 0
}


# 函数：调用 API
# 参数：$1 - IP 地址
# 参数：$2 - 端口号
# 参数：$3 - HTTP 密钥
# 参数：$4 - 协议 (http 或 https, 默认 http)
# 参数：$5 - API 路径 (例如: s2s_get_version, 默认 s2s_get_version)
call_api_with_curl() {
    IP="$1"
    PORT="$2"
    HTTP_KEY="$3"
    SCHEME="${4:-http}" # 默认 http
    API="${5:-${API_GET_VERSION}}" # 默认使用预定义的 API_GET_VERSION

    echo "正在使用 ${SCHEME}://${IP}:${PORT}  ，调用 API: ${API}..."
    curl --location --request POST "${SCHEME}://${IP}:${PORT}/v2/rpc/${API}?http_key=${HTTP_KEY}&unwrap" \
        --header 'Accept: application/json' \
        --header 'Content-Type: application/json' \
        --data-raw '{}' | jq .
    echo ""
}

# 函数：遍历 IP 地址并执行 curl 命令
# 参数：$1 - IP 地址列表 (多行字符串)
# 参数：$2 - HTTP 密钥环境变量的名称 (例如: NKMAD_NAKAMA_PROD_HTTP_KEY)
# 参数：$3 - 端口号 (例如: 7350)
# 参数：$4 - 协议 (http 或 https, 默认 http)
# 参数：$5 - API 路径 (例如: s2s_get_version, 默认 s2s_get_version)
run_curl_on_ips() {
    PUBLIC_IPS="$1"
    HTTP_KEY_VAR_NAME="$2"
    PORT="$3"
    SCHEME="${4:-http}"
    API="${5:-${API_GET_VERSION}}" # 默认使用预定义的 API_GET_VERSION
    HTTP_KEY=""

    # 动态获取 HTTP 密钥的值
    eval "HTTP_KEY=\$$HTTP_KEY_VAR_NAME"

    if [ -z "${HTTP_KEY}" ]; then
        echo "错误：环境变量 '${HTTP_KEY_VAR_NAME}' 未设置。请先设置该变量。"
        return 1
    fi

    echo "开始对每个 IP 地址执行 curl 命令..."

    # 使用 Bash 数组来遍历 IP 地址，更简洁
    IFS=$'\n' read -r -d '' -a IP_ARRAY <<< "$PUBLIC_IPS"

    for IP in "${IP_ARRAY[@]}"; do
        IP=$(echo "${IP}" | tr -d '[:space:]') # 移除 IP 地址可能包含的任何空白字符
        if [ -n "${IP}" ]; then # 确保 IP 不为空
            call_api_with_curl "${IP}" "${PORT}" "${HTTP_KEY}" "${SCHEME}" "${API}"
        fi
    done

    echo "所有 IP 地址的 curl 命令执行完毕。"
    return 0
}

# 新增函数：更新 Route 53 A 记录
# 参数：$1 - IP 地址
# 参数：$2 - 索引 (用于构造记录名 nodeX)
# 参数：$3 - Hosted Zone ID
# 参数：$4 - 基础域名 (例如 yourdomain.com)
# 参数：$5 - TTL (秒，默认为 300)
update_route53_record_for_ip() {
    IP="$1"
    INDEX="$2"
    HOSTED_ZONE_ID="$3"
    BASE_DOMAIN="$4"
    TTL="${5:-300}" # Default TTL to 300 seconds

    RECORD_NAME="prod-node${INDEX}.${BASE_DOMAIN}"

    echo "正在更新 Route 53 记录: ${RECORD_NAME} -> ${IP} (Hosted Zone ID: ${HOSTED_ZONE_ID})..." >&2

    # Constructing the JSON payload for Route 53
    CHANGE_BATCH_JSON=$(cat <<EOF
{
  "Comment": "Update A record for ${RECORD_NAME} to ${IP}",
  "Changes": [
    {
      "Action": "UPSERT",
      "ResourceRecordSet": {
        "Name": "${RECORD_NAME}",
        "Type": "A",
        "TTL": ${TTL},
        "ResourceRecords": [
          {
            "Value": "${IP}"
          }
        ]
      }
    }
  ]
}
EOF
)

    # Execute the aws route53 command
    # Redirect stdout and stderr to /dev/null for clean output, or just stderr if needed for debugging
    aws route53 change-resource-record-sets \
        --hosted-zone-id "${HOSTED_ZONE_ID}" \
        --change-batch "${CHANGE_BATCH_JSON}" \
        2>&1 >/dev/null

    if [ $? -eq 0 ]; then
        echo "成功更新 Route 53 记录: ${RECORD_NAME} -> ${IP}" >&2
    else
        echo "错误：更新 Route 53 记录失败: ${RECORD_NAME} -> ${IP}" >&2
        return 1
    fi
}


# 生产环境操作函数
run_prod_operations() {
    echo "执行生产环境操作..."
    ASG_NAME="nkmad-cluster-nakama-db-rpc-asg-prod" # 生产环境的 ASG 名称
    HTTP_KEY_VAR="NKMAD_NAKAMA_PROD_HTTP_KEY" # 生产环境的 HTTP 密钥环境变量名
    PORT="7450" # 生产环境的端口
    SCHEME="https" # 生产环境的协议
    API="${API_GET_VERSION}" # 生产环境的 API 路径，默认使用预定义的 API_GET_VERSION

    echo ">>>>>>>> Prod Dev Console https://nkmad-dev-console.pwghub.com/    ${D_C_U} ${D_C_P}"
    echo ">>>>>>>> Prod  Console https://console.nkmad-cluster.pwglab.com:7351/  ${P_C_U} ${P_C_P}"

    # 获取所有实例的公网和私网IP
    INSTANCE_IPS=$(get_asg_instance_ips "${ASG_NAME}")
    if [ $? -ne 0 ]; then
        echo "无法获取实例 IP，中止生产环境操作。" >&2
        return 1
    fi

    # 准备摘要表
    declare -a SUMMARY_TABLE_ROWS
    TABLE_HEADER=$(printf "%-45s %-20s %-20s" "Node Record" "Public IP" "Private IP")
    SUMMARY_TABLE_ROWS+=("$TABLE_HEADER")
    SUMMARY_TABLE_ROWS+=("$(printf '%*s' "${#TABLE_HEADER}" '' | tr ' ' '-')")


    # 遍历 IP 并打印初始链接
    while read -r PUBLIC_IP PRIVATE_IP; do
        if [ -n "${PUBLIC_IP}" ]; then
            PUBLIC_IP=$(echo "${PUBLIC_IP}" | tr -d '[:space:]')
            PRIVATE_IP=$(echo "${PRIVATE_IP}" | tr -d '[:space:]')
            echo ">>>>>>>> Prod Node Console http://${PUBLIC_IP}:7351 (Private IP: ${PRIVATE_IP})  ${P_C_U} ${P_C_P}"
            echo ">>>>>>>> Prod Node 1Panel http://${PUBLIC_IP}:24914/tXTAGMf9P1  ${OnePanel_U} ${OnePanel_P}"
        fi
    done <<< "$INSTANCE_IPS"


    ROUTE53_HOSTED_ZONE_ID="Z06363652DU4H3FACSAFC"
    ROUTE53_BASE_DOMAIN="nkmad-cluster.pwglab.com"

    # 更新 Route 53 记录
    if [ -z "${ROUTE53_HOSTED_ZONE_ID}" ] || [ "${ROUTE53_HOSTED_ZONE_ID}" = "YOUR_PROD_HOSTED_ZONE_ID" ]; then
        echo "警告：未设置生产环境的 Route 53 Hosted Zone ID (ROUTE53_HOSTED_ZONE_ID)。将跳过 Route 53 更新。" >&2
    elif [ -z "${ROUTE53_BASE_DOMAIN}" ] || [ "${ROUTE53_BASE_DOMAIN}" = "yourdomain.com" ]; then
        echo "警告：未设置生产环境的 Route 53 基础域名 (ROUTE53_BASE_DOMAIN)。将跳过 Route 53 更新。" >&2
    else
        echo ""
        echo "开始更新 Route 53 记录并收集摘要信息..." >&2
        INDEX=1
        while read -r PUBLIC_IP PRIVATE_IP; do
            PUBLIC_IP=$(echo "${PUBLIC_IP}" | tr -d '[:space:]')
            PRIVATE_IP=$(echo "${PRIVATE_IP}" | tr -d '[:space:]')

            if [ -n "${PUBLIC_IP}" ]; then
                update_route53_record_for_ip "${PUBLIC_IP}" "${INDEX}" "${ROUTE53_HOSTED_ZONE_ID}" "${ROUTE53_BASE_DOMAIN}" "${ROUTE53_TTL}"
                RECORD_NAME="prod-node${INDEX}.${ROUTE53_BASE_DOMAIN}"

                # 打印包含私网IP的信息
                echo "映射信息: ${RECORD_NAME} -> 公网IP: ${PUBLIC_IP}, 私网IP: ${PRIVATE_IP}"
                echo ">>>>>>>> Prod Node Console http://${RECORD_NAME}:7351  ${P_C_U} ${P_C_P}"
                echo ">>>>>>>> Prod Node 1Panel http://${RECORD_NAME}:24914/tXTAGMf9P1    ${OnePanel_U} ${OnePanel_P}"

                # 添加行到摘要表
                TABLE_ROW=$(printf "%-45s %-20s %-20s" "${RECORD_NAME}" "${PUBLIC_IP}" "${PRIVATE_IP}")
                SUMMARY_TABLE_ROWS+=("$TABLE_ROW")

                INDEX=$((INDEX + 1))
            fi
        done <<< "$INSTANCE_IPS"
        echo "Route 53 记录更新完毕。" >&2
    fi

    # 打印最终的摘要表
    echo ""
    echo "============================= 节点 IP 映射摘要 ============================="
    printf "%s\n" "${SUMMARY_TABLE_ROWS[@]}"
    echo "=============================================================================="
    echo ""

    # 提取公网IP列表以调用API
    PUBLIC_IPS_LIST=$(echo "$INSTANCE_IPS" | awk '{print $1}')
    if [ -n "${PUBLIC_IPS_LIST}" ]; then
        run_curl_on_ips "${PUBLIC_IPS_LIST}" "${HTTP_KEY_VAR}" "7350" "http" "${API_GET_VERSION}"
    fi
}

# 开发环境操作函数
run_dev_operations() {
    echo "执行开发环境操作..."
    # 开发环境不需要 ASG，直接针对指定的 IP 调用 API
    DEV_IP="nkmad-dev.pwghub.com" # 示例：开发环境的指定 IP，请根据实际情况修改
    HTTP_KEY_VAR="NKMAD_NAKAMA_DEV_HTTP_KEY" # 开发环境的 HTTP 密钥环境变量名
    PORT="443" # 开发环境的端口
    SCHEME="https" # 开发环境的协议
    API="${API_GET_VERSION}" # 开发环境的 API 路径，默认使用预定义的 API_GET_VERSION

    if [ -z "${DEV_IP}" ] || [ "${DEV_IP}" = "127.0.0.1" ]; then
        echo "警告：未设置开发环境的指定 IP (DEV_IP)。请根据实际情况修改。"
    fi

    HTTP_KEY=""
    eval "HTTP_KEY=\$$HTTP_KEY_VAR"
    if [ -z "${HTTP_KEY}" ]; then
        echo "错误：环境变量 '${HTTP_KEY_VAR}' 未设置。请先设置该变量。"
        return 1
    fi

    echo "针对开发环境指定 IP (${DEV_IP}) 调用 API..."
    call_api_with_curl "${DEV_IP}" "${PORT}" "${HTTP_KEY}" "${SCHEME}" "${API_GET_VERSION}"
}

# 本地环境操作函数
run_local_operations() {
    echo "执行本地环境操作..."
    # 本地环境不需要 ASG，直接针对指定的 IP 调用 API
    LOCAL_IP="127.0.0.1" # 示例：本地环境的指定 IP，请根据实际情况修改
    HTTP_KEY_VAR="NKMAD_NAKAMA_LOCAL_HTTP_KEY" # 本地环境的 HTTP 密钥环境变量名
    PORT="7350" # 本地环境的端口
    SCHEME="http" # 本地环境的协议
    API="${API_GET_VERSION}" # 本地环境的 API 路径，默认使用预定义的 API_GET_VERSION

    if [ -z "${LOCAL_IP}" ] || [ "${LOCAL_IP}" = "127.0.0.1" ]; then
        echo "警告：未设置本地环境的指定 IP (LOCAL_IP)。请根据实际情况修改。"
    fi

    HTTP_KEY=""
    eval "HTTP_KEY=\$$HTTP_KEY_VAR"
    if [ -z "${HTTP_KEY}" ]; then
        echo "错误：环境变量 '${HTTP_KEY_VAR}' 未设置。请先设置该变量。"
        return 1
    fi

    echo "针对本地环境指定 IP (${LOCAL_IP}) 调用 API..."
    call_api_with_curl "${LOCAL_IP}" "${PORT}" "${HTTP_KEY}" "${SCHEME}" "${API_GET_VERSION}"
}

# 根据环境调用相应的操作函数
case $ENV in
  "${PROD_ENV}")
    run_dev_operations
    run_prod_operations
    ;;
  "${DEV_ENV}") run_dev_operations;;
  "${LOCAL_ENV}") run_local_operations;;
  *) # 默认情况已在脚本开头处理，这里不会再执行到
    ;;
esac