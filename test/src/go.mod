module github.com/cloudposse/terraform-aws-ec2-bastion-server

go 1.13

require (
	github.com/agext/levenshtein v1.2.3 // indirect
	github.com/gruntwork-io/gruntwork-cli v0.7.0 // indirect
	github.com/gruntwork-io/terratest v0.34.7
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/hcl/v2 v2.9.1 // indirect
	github.com/hashicorp/terraform-json v0.10.0 // indirect
	github.com/jinzhu/copier v0.2.9 // indirect
	github.com/mitchellh/go-wordwrap v1.0.1 // indirect
	github.com/stretchr/testify v1.7.0
	github.com/zclconf/go-cty v1.8.1 // indirect
	golang.org/x/crypto v0.0.0-20210415154028-4f45737414dc // indirect
	golang.org/x/net v0.0.0-20210420072503-d25e30425868 // indirect
	golang.org/x/sys v0.0.0-20210420072515-93ed5bcd2bfe // indirect
	gopkg.in/yaml.v3 v3.0.0-20210107192922-496545a6307b // indirect
)
