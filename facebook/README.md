# Facebook SDK

## Facebook Login

### Google Play

```bash
# https://stackoverflow.com/a/47609031/2000468
# SHA1 ->  hex format -> bash64
gen-hash-key-gp-fb-upload:
	echo F2:DB:5B:BA:98:09:1F:D2:F7:79:DD:0E:82:53:12:14:42:5E:E6:50 | xxd -r -p | openssl base64
# should same with the keystore generated one

gen-hash-key-gp-fb-sign:
	echo 1A:BA:41:8B:64:F4:42:B9:90:D4:00:64:7E:08:02:F2:56:40:AF:2C | xxd -r -p | openssl base64

```

这里需要从 Google Play 上获取 Sha1 值, 这里有两个, 一个是 upload, 一个是 Sign, 因为我们用的是 google sign, 所以不一样.

### Build-in keystore


关于 key_hash

https://developers.facebook.com/docs/android/getting-started/

Create a Release Key Hash
To authenticate the exchange of information between your app and the Facebook, you need to generate a release key hash and add this to the Android settings within your Facebook App ID. Without this, your Facebook integration may not work properly when you release your app to the store.

In a previous step, you should have updated your Facebook Developer Settings with the key hashes for your development environments.

When publishing your app, it is typically signed with a different signature to your development environment. Therefore, you want to make sure you create a Release Key Hash and add this to the Android settings for Facebook App ID.

To generate a hash of your release key, run the following command on Mac or Windows substituting your release key alias and the path to your keystore.

On Mac OS, run:

```bash
keytool -exportcert -alias <RELEASE_KEY_ALIAS> -keystore <RELEASE_KEY_PATH> | openssl sha1 -binary | openssl base64
```
On Windows, you need the following:

Key and Certificate Management Tool (keytool) from the Java Development Kit
OpenSSL for Windows Library from the Google Code Archive
Run the following command in a command prompt in the Java SDK folder:

keytool -exportcert -alias <RELEASE_KEY_ALIAS> -keystore <RELEASE_KEY_PATH> | PATH_TO_OPENSSL_LIBRARY\bin\openssl sha1 -binary | PATH_TO_OPENSSL_LIBRARY\bin\openssl base64
Make sure to use the password that you set when you first created the release key.

This command should generate a 28 characher string. Copy and paste this Release Key Hash into your Facebook App ID's Android settings.


You should also check that your Facebook App ID's Android setting also contain the correct package name and main activity class for your Android package.