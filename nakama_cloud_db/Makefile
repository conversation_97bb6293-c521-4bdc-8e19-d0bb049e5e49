ssh-work-nkmfd:
	ssh -o StrictHostKeyChecking=no -i "../ci/nkmfd_dev.pem" <EMAIL>

scp-dump1:
	scp -o StrictHostKeyChecking=no -i "../ci/nkmfd_dev.pem" dump1.sh <EMAIL>:~/dump1.sh

scp-dump2:
	scp -o StrictHostKeyChecking=no -i "../ci/nkmfd_dev.pem" dump2.sh <EMAIL>:~/dump2.sh

scp-restore1:
	scp -o StrictHostKeyChecking=no -i "../ci/nkmfd_dev.pem" restore1.sh <EMAIL>:~/restore1.sh

scp-dump1-log:
	scp -o StrictHostKeyChecking=no -i "../ci/nkmfd_dev.pem"  <EMAIL>:~/dump1-2024-12-10-test1.log dump1-2024-12-10-test1.log


scp-dump1:
	scp -o StrictHostKeyChecking=no -i "../ci/nkmfd_prod.pem" dump1.sh <EMAIL>:~/dump1.sh



ssh-prod-nkmfd-node-0-odd:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_prod.pem" <EMAIL>