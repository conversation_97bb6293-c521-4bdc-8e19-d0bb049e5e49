

dump T1


dump T2

# 使用方法

首先创建 Ubuntu instance
然后安装必要的依赖包
```bash
sudo apt install postgresql-client-16
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
mkdir ~/.aws
touch ~/.aws/credentials
vim ~/.aws/credentials
#本地的 credentials

```
配置 security 访问 RDS

```bash
# instance 添加 secruity group
# nkmfd_ec2_to_rds_outbound_for_ec2
# 从而可以访问 RDS
```

```bash
# 拷贝 脚本
scp -o StrictHostKeyChecking=no -i "../ci/nkmfd_dev.pem" dump1.sh <EMAIL>:~/dump1.sh
# 执行脚本
nohup bash dump1.sh &
# 查看进度
tail -f nohup.out
# pg_dump: dumping contents of table "public.console_user"
# pg_dump: dumping contents of table "public.group_edge"
# pg_dump: dumping contents of table "public.groups"
# pg_dump: dumping contents of table "public.leaderboard"
# pg_dump: dumping contents of table "public.leaderboard_record"
# pg_dump: dumping contents of table "public.message"
# pg_dump: dumping contents of table "public.migration_info"
# pg_dump: dumping contents of table "public.notification"
# pg_dump: dumping contents of table "public.purchase"
# pg_dump: dumping contents of table "public.storage"
```


# 其他
```bash

/usr/local/bin/rclone copy 2024-12-10 google-ci:db_dump/nkmfd
```

我们将在 work.nkmfd.pwglab.com 节点进行操作

```bash
ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_dev.pem" <EMAIL>
```

```bash
nohup time pg_dump -h nkmfd.rds.prod.pwglab.com -p 5432 -U nkmfd_admin -Fc -b -v -f nkmfd-2024-12-11-first.sql -d nkmfd_db &

ixU8cHt8e0tq8BDeIRdu_gn

tail -f nohup.out
```


```bash
ssh-work-nkmfd:
	ssh -o StrictHostKeyChecking=no -i "ci/nkmfd_dev.pem" <EMAIL>
	
scp -o StrictHostKeyChecking=no -i "ci/nkmfd_dev.pem" dupm1.sh <EMAIL>:~/dupm1.sh

nohup bash dump1.sh &
nohup bash restore1.sh &
```


测试导入

```bash
pg_restore -h localhost -p 5432 -U nkmfd_admin -d nkmfd_db -v nkmfd_2024-12-10_10-02-42.sql
pg_restore -h localhost -p 5432 -U postgres -d test_db -v nkmfd-2024-12-09.sql
psql -h work.nkmfd.pwglab.com -U nkmfd_admin -d nkmfd_db -p 5432



psql -h localhost -U nkmfd_admin -d nkmfd_db -p 5432
```


server 设定

4 是 cloud
5 是 dev2

我们不设定 downtime, 所以已更新结束时间就要 < 当前时间, 比如用 10 点的时间

Epoch timestamp: 1733911200
Timestamp in milliseconds: 1733911200000
Date and time (GMT): Wednesday, 11 December 2024 10:00:00
Date and time (your time zone): Wednesday, 11 December 2024 18:00:00 GMT+08:00

```json
{
  "migratedServerId": 4,
  "migratedServerUrl": "",
  "serverMaintenance": true,
  "expectedMaintenanceFinishTs": 1733911200
}
```