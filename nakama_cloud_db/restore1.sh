#!/bin/bash

# 配置区域
HOST="localhost"
PORT=5432
USER="nkmfd_admin"
DB="nkmfd_db"
S3_PATH="migration"
PASSWORD="nkmfd_admin"

# 获取当前时间作为文件名
SQL_FILE="nkmfd_2024-12-10_10-02-42.sql"

# 设置 PGPASSWORD 环境变量避免手动输入密码
export PGPASSWORD="$PASSWORD"

# 开始导出数据库
echo "Starting pg_store at $(date)"
START_DUMP=$(date +%s)

pg_restore -h "$HOST" -p "$PORT" -U "$USER" -d "$DB" -v "$SQL_FILE"

END_DUMP=$(date +%s)
DUMP_DURATION=$((END_DUMP - START_DUMP))
echo "Database restore completed in $DUMP_DURATION seconds."

# 清理环境变量
unset PGPASSWORD

# 总结
echo "Backup process completed."
echo "Dump Duration: $DUMP_DURATION seconds"