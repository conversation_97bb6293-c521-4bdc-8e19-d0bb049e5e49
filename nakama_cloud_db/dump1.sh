#!/bin/bash

# 配置区域
HOST="nkmfd.rds.prod.pwglab.com"
PORT=5432
USER="nkmfd_admin"
DB="nkmfd_db"
S3_BUCKET="nkmfd-dump"
S3_PATH="migration"
PASSWORD="ixU8cHt8e0tq8BDeIRdu_gn"

# 获取当前时间作为文件名
TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")
SQL_FILE="nkmfd_dump1_${TIMESTAMP}.sql"

# 设置 PGPASSWORD 环境变量避免手动输入密码
export PGPASSWORD="$PASSWORD"

# 开始导出数据库
echo "Starting pg_dump at $(date)"
START_DUMP=$(date +%s)

pg_dump -h "$HOST" -p "$PORT" -U "$USER" -Fc -b -v -f "$SQL_FILE" -d "$DB"

END_DUMP=$(date +%s)
DUMP_DURATION=$((END_DUMP - START_DUMP))
echo "Database dump completed in $DUMP_DURATION seconds."

# 上传到 S3
echo "Starting S3 upload at $(date)"
START_UPLOAD=$(date +%s)

aws s3 cp "$SQL_FILE" "s3://$S3_BUCKET/$S3_PATH/$SQL_FILE"

END_UPLOAD=$(date +%s)
UPLOAD_DURATION=$((END_UPLOAD - START_UPLOAD))
echo "S3 upload completed in $UPLOAD_DURATION seconds."

# 清理环境变量
unset PGPASSWORD

# 总结
echo "Backup process completed."
echo "Dump Duration: $DUMP_DURATION seconds"
echo "Upload Duration: $UPLOAD_DURATION seconds"
echo "Backup file: s3://$S3_BUCKET/$S3_PATH/$SQL_FILE"