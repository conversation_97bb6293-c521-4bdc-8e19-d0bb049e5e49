data "aws_vpc" "default" {
  default = true
}
provider "aws" {
  region = "us-east-1"
}

locals {
  app_name = var.app_name
}

resource "aws_security_group" "app_dev_allow_tls" {
  name        = "${local.app_name}_dev_allow_tls"
  description = "Allow TLS inbound traffic to ${local.app_name}"

  ingress {
    description      = "TLS from VPC"
    from_port        = 443
    to_port          = 443
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "${local.app_name}_dev_allow_tls"
    Env  = "dev"
    App  = local.app_name
  }
}
resource "aws_security_group" "app_dev_allow_ssh" {
  name        = "${local.app_name}_dev_allow_ssh"
  description = "Allow SSH inbound traffic to ${local.app_name}"

  ingress {
    description      = "SSH 22 from VPC"
    from_port        = 22
    to_port          = 22
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "${local.app_name}_dev_allow_ssh"
    Env  = "dev"
    App  = local.app_name
  }
}
resource "aws_security_group" "app_dev_allow_service" {
  name        = "${local.app_name}_dev_allow_service"
  description = "Allow service inbound traffic to ${local.app_name}"

  ingress {
    description      = "7349-7351"
    from_port        = 7349
    to_port          = 7351
    protocol         = "udp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "7349-7351"
    from_port        = 7349
    to_port          = 7351
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "9090"
    from_port        = 9090
    to_port          = 9090
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "${local.app_name}_dev_allow_service"
    Env  = "dev"
    App  = local.app_name
  }
}
