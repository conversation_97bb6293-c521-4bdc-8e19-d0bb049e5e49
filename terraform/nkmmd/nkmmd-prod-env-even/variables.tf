# ---------------------------------------------------------------------------------------------------------------------
# REQUIRED PARAMETERS
# You must provide a value for each of these parameters.
# ---------------------------------------------------------------------------------------------------------------------
/* Required update for different app */
variable "app_name" {
  description = ""
  default     = "nkmmd"
}

/* check the backend project docker tags */
variable "game_service_version" {
  description = ""
  default     = "1.0.0"
}

// ======================================================
/* Optional update for different app */
variable "instance_type" {
  description = "The type of EC2 Instances to run (e.g. t2.micro)"
  type        = string
  default     = "t3.medium"
}

variable "cpu_credits_type" {
  description = "The name to use for EC2 instance"
  type        = string
  default     = "standard"
}

variable "ami_owner" {
  description = "Filter for the AMI owner"
  default     = "self"
}

/*
0 -> use cloud db
1 -> use RDS db
*/
variable "use_rds_db" {
  description = ""
  default     = 1
}

variable "live_game_subdomain" {
  description = "the live game subdomain"
  // default = "live-game"
  default = "live-game"
}

variable "suffix" {
  description = "suffix"
  default     = "-even"
}
