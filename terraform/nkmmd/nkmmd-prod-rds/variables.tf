variable "app_name" {
  description = ""
  default     = "nkmmd"
}

variable "namespace" {
  description = ""
  default     = "nakama-rds"
}

module "shared" {
  source   = "./shared"
  app_name = var.app_name
}

locals {
  # Untested but should work in theory
  app_name      = var.app_name
  namespace     = var.namespace
  host_name     = "${local.app_name}.rds.prod"
  database_name = "${local.app_name}_db"
  database_user = "${local.app_name}_admin"
  allowed_cidr_blocks = [
    "*************/32", // PW Lightsail Tokyo
    "*************/32", // PW Lightsail London
    "************/32"   // RM Dev Ireland
  ]
  associate_security_group_ids = [
    module.shared.sg_app_rds_to_ec2_inbound_for_rds_id
  ]
}

#variable "host_name" {
#  type = string
#  #  default     = local.host_name
#  description = "The DB host name created in Route53"
#}
#
#/*
#[OLD Doc]
#https://us-east-1.console.aws.amazon.com/ec2/v2/home?region=us-east-1#SecurityGroup:groupId=sg-0bc9f1bf1c175d5eb
#
#this security group is manually created after try to connect RDS to EC2 instance:
#1. create ec2-rds-1 security group, this will allow outbound to rds-ec2-1  -> assign to EC2 instance
#2. create rds-ec2-1 security group, this will allow inbound from ec2-rds-1 -> assign to RDS database
#
#[Latest Doc]
#this security group is created by shared
#https://github.com/playwindgames/devops-nakama/blob/main/terraform/nakama-backend/nakama-backend-shared/shared/security.tf#L309-L309
#*/
#variable "associate_security_group_ids" {
#  type = list(string)
#  #  default = [
#  #    module.shared.sg_app_rds_to_ec2_inbound_for_rds_id
#  #  ]
#  description = "The IDs of the existing security groups to associate with the DB instance"
#}
#
#variable "database_name" {
#  type = string
#  #  default     = local.database_name
#  description = "The name of the database to create when the DB instance is created"
#}
#
#variable "database_user" {
#  type = string
#  #  default     = local.database_user
#  description = "Username for the primary DB user. Required unless a `snapshot_identifier` or `replicate_source_db` is provided."
#}
#
#variable "allowed_cidr_blocks" {
#  type = list(string)
#  /*TODO_FINISH: add some whitelist IP of our VPN */
#  #  default     = local.whitelist_ips
#  description = "The whitelisted CIDRs which to allow `ingress` traffic to the DB instance"
#}

/*======================= Unified Variables ======================= */

variable "dns_zone_id" {
  type = string
  # pwglab.com
  # https://us-east-1.console.aws.amazon.com/route53/v2/hostedzones?region=us-east-1#ListRecordSets/Z06363652DU4H3FACSAFC
  default     = "Z06363652DU4H3FACSAFC"
  description = "The ID of the DNS Zone in Route53 where a new DNS record will be created for the DB host name"
}


variable "security_group_ids" {
  type = list(string)
  /*TODO_FINISH: */
  default     = []
  description = "The IDs of the security groups from which to allow `ingress` traffic to the DB instance"
}

/* set sensitive data in the Terraform cloud */
variable "database_password" {
  type        = string
  default     = null
  description = "Password for the primary DB user. Required unless a `snapshot_identifier` or `replicate_source_db` is provided."
}

variable "database_port" {
  type        = number
  default     = 5432
  description = "Database port (_e.g._ `3306` for `MySQL`). Used in the DB Security Group to allow access to the DB instance from the provided `security_group_ids`"
}

variable "deletion_protection" {
  type        = bool
  description = "Set to true to enable deletion protection on the RDS instance"
  default     = false
}

variable "multi_az" {
  type        = bool
  description = "Set to true if multi AZ deployment must be supported"
  default     = false
}

variable "storage_type" {
  type        = string
  description = "One of 'standard' (magnetic), 'gp2' (general purpose SSD), 'gp3' (general purpose SSD), or 'io1' (provisioned IOPS SSD)"
  default     = "gp2"
}

variable "storage_encrypted" {
  type        = bool
  description = "(Optional) Specifies whether the DB instance is encrypted. The default is false if not specified"
  default     = false
}

variable "iops" {
  type        = number
  description = "The amount of provisioned IOPS. Setting this implies a storage_type of 'io1'. Default is 0 if rds storage type is not 'io1'"
  default     = 0
}

variable "storage_throughput" {
  type        = number
  description = "The storage throughput value for the DB instance. Can only be set when `storage_type` is `gp3`. Cannot be specified if the `allocated_storage` value is below a per-engine threshold."
  default     = null
}

variable "allocated_storage" {
  type        = number
  description = "The allocated storage in GBs. Required unless a `snapshot_identifier` or `replicate_source_db` is provided."
  /*
  Provisioning less than 100 GiB of General Purpose (SSD) storage for high throughput workloads could result in higher latencies upon exhaustion of the initial General Purpose (SSD) IO credit balance. Learn more
  https://docs.aws.amazon.com//AmazonRDS/latest/UserGuide/CHAP_Storage.html#Concepts.Storage.GeneralSSD
  */
  default = 20
}

variable "max_allocated_storage" {
  type        = number
  description = "The upper limit to which RDS can automatically scale the storage in GBs"
  default     = 200
}

variable "engine" {
  type        = string
  description = "Database engine type. Required unless a `snapshot_identifier` or `replicate_source_db` is provided."
  default     = "postgres"
  # http://docs.aws.amazon.com/cli/latest/reference/rds/create-db-instance.html
  # - mysql
  # - postgres
  # - oracle-*
  # - sqlserver-*
  # - mariadb
}

# Check PostgresSQL versions
# https://docs.aws.amazon.com/AmazonRDS/latest/PostgreSQLReleaseNotes/postgresql-release-calendar.html
variable "engine_version" {
  type        = string
  description = "Database engine version, depends on engine type."
  default     = "16.1"
  # http://docs.aws.amazon.com/cli/latest/reference/rds/create-db-instance.html
}

variable "major_engine_version" {
  type        = string
  description = "Database MAJOR engine version, depends on engine type"
  default     = "16"
  # https://docs.aws.amazon.com/cli/latest/reference/rds/create-option-group.html
}

variable "charset_name" {
  type        = string
  description = "The character set name to use for DB encoding. [Oracle & Microsoft SQL only](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_instance#character_set_name). For other engines use `db_parameter`"
  default     = null
}

variable "license_model" {
  type        = string
  description = "License model for this DB. Optional, but required for some DB Engines. Valid values: license-included | bring-your-own-license | general-public-license"
  default     = ""
}

variable "instance_class" {
  type        = string
  description = "Class of RDS instance"
  default     = "db.t3.small"
  # https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Concepts.DBInstanceClass.html
}

# This is for custom parameters to be passed to the DB
# We're "cloning" default ones, but we need to specify which should be copied
# Check the Console add DB manually page  https://us-east-1.console.aws.amazon.com/rds/home?region=us-east-1#launch-dbinstance:;isHermesCreate=true
# aws rds describe-db-engine-versions --query "DBEngineVersions[].DBParameterGroupFamily" --engine postgres
variable "db_parameter_group" {
  type        = string
  description = "The DB parameter group family name. The value depends on DB engine used. See [DBParameterGroupFamily](https://docs.aws.amazon.com/AmazonRDS/latest/APIReference/API_CreateDBParameterGroup.html#API_CreateDBParameterGroup_RequestParameters) for instructions on how to retrieve applicable value."
  default     = "postgres16"
  # "mysql5.6"
  # "postgres9.5"
}

variable "publicly_accessible" {
  type        = bool
  description = "Determines if database can be publicly available (NOT recommended)"
  default     = true
}

variable "subnet_ids" {
  /*TODO_FINISH: use default*/
  description = "List of subnet IDs for the DB. DB instance will be created in the VPC associated with the DB subnet group provisioned using the subnet IDs. Specify one of `subnet_ids`, `db_subnet_group_name` or `availability_zone`"
  type        = list(string)
  default     = []
}

variable "availability_zone" {
  type        = string
  default     = null
  description = "The AZ for the RDS instance. Specify one of `subnet_ids`, `db_subnet_group_name` or `availability_zone`. If `availability_zone` is provided, the instance will be placed into the default VPC or EC2 Classic"
}

variable "db_subnet_group_name" {
  type        = string
  default     = null
  description = "Name of DB subnet group. DB instance will be created in the VPC associated with the DB subnet group. Specify one of `subnet_ids`, `db_subnet_group_name` or `availability_zone`"
}

#variable "vpc_id" {
#  type        = string
#  description = "VPC ID the DB instance will be created in"
#}

variable "auto_minor_version_upgrade" {
  type        = bool
  description = "Allow automated minor version upgrade (e.g. from Postgres 9.5.3 to Postgres 9.5.4)"
  default     = true
}

variable "allow_major_version_upgrade" {
  type        = bool
  description = "Allow major version upgrade"
  default     = false
}

variable "apply_immediately" {
  type        = bool
  description = "Specifies whether any database modifications are applied immediately, or during the next maintenance window"
  default     = false
}

variable "maintenance_window" {
  type        = string
  description = "The window to perform maintenance in. Syntax: 'ddd:hh24:mi-ddd:hh24:mi' UTC "
  default     = "Mon:03:00-Mon:04:00"
}

variable "skip_final_snapshot" {
  type        = bool
  description = "If true (default), no snapshot will be made before deleting DB"
  default     = true
}

variable "copy_tags_to_snapshot" {
  type        = bool
  description = "Copy tags from DB to a snapshot"
  default     = true
}

variable "backup_retention_period" {
  type        = number
  description = "Backup retention period in days. Must be > 0 to enable backups"
  default     = 7
}

variable "backup_window" {
  type        = string
  description = "When AWS can perform DB snapshots, can't overlap with maintenance window"
  default     = "22:00-03:00"
}

variable "db_parameter" {
  type = list(object({
    apply_method = string
    name         = string
    value        = string
  }))
  default     = []
  description = "A list of DB parameters to apply. Note that parameters may differ from a DB family to another"
}

variable "db_options" {
  type = list(object({
    db_security_group_memberships  = list(string)
    option_name                    = string
    port                           = number
    version                        = string
    vpc_security_group_memberships = list(string)

    option_settings = list(object({
      name  = string
      value = string
    }))
  }))

  default     = []
  description = "A list of DB options to apply with an option group. Depends on DB engine"
}

variable "snapshot_identifier" {
  /*TODO_FINISH: "rds:production-2015-06-26-06-05"? */
  type        = string
  description = "Snapshot identifier e.g: rds:production-2019-06-26-06-05. If specified, the module create cluster from the snapshot"
  default     = null
}

variable "final_snapshot_identifier" {
  type        = string
  description = "Final snapshot identifier e.g.: some-db-final-snapshot-2019-06-26-06-05"
  default     = ""
}

variable "parameter_group_name" {
  type        = string
  description = "Name of the DB parameter group to associate"
  default     = ""
}

variable "option_group_name" {
  type        = string
  description = "Name of the DB option group to associate"
  default     = "mysql-options"
}

variable "kms_key_arn" {
  type        = string
  description = "The ARN of the existing KMS key to encrypt storage"
  default     = ""
}

variable "performance_insights_enabled" {
  type        = bool
  default     = false
  description = "Specifies whether Performance Insights are enabled."
}

variable "performance_insights_kms_key_id" {
  type        = string
  default     = null
  description = "The ARN for the KMS key to encrypt Performance Insights data. Once KMS key is set, it can never be changed."
}

variable "performance_insights_retention_period" {
  type        = number
  default     = 7
  description = "The amount of time in days to retain Performance Insights data. Either 7 (7 days) or 731 (2 years)."
}

variable "enabled_cloudwatch_logs_exports" {
  type        = list(string)
  default     = []
  description = "List of log types to enable for exporting to CloudWatch logs. If omitted, no logs will be exported. Valid values (depending on engine): alert, audit, error, general, listener, slowquery, trace, postgresql (PostgreSQL), upgrade (PostgreSQL)."
}

variable "ca_cert_identifier" {
  type        = string
  description = "The identifier of the CA certificate for the DB instance"
  default     = "rds-ca-2019"
}

variable "monitoring_interval" {
  description = "The interval, in seconds, between points when Enhanced Monitoring metrics are collected for the DB instance. To disable collecting Enhanced Monitoring metrics, specify 0. Valid Values are 0, 1, 5, 10, 15, 30, 60."
  default     = "0"
}

variable "monitoring_role_arn" {
  type        = string
  description = "The ARN for the IAM role that permits RDS to send enhanced monitoring metrics to CloudWatch Logs"
  default     = null
}

variable "iam_database_authentication_enabled" {
  type        = bool
  description = "Specifies whether or mappings of AWS Identity and Access Management (IAM) accounts to database accounts is enabled"
  default     = false
}

variable "replicate_source_db" {
  type        = string
  description = "Specifies that this resource is a Replicate database, and to use this value as the source database. This correlates to the `identifier` of another Amazon RDS Database to replicate (if replicating within a single region) or ARN of the Amazon RDS Database to replicate (if replicating cross-region). Note that if you are creating a cross-region replica of an encrypted database you will also need to specify a `kms_key_id`. See [DB Instance Replication](https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Overview.Replication.html) and [Working with PostgreSQL and MySQL Read Replicas](https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_ReadRepl.html) for more information on using Replication."
  default     = null
}

variable "timezone" {
  type        = string
  description = "Time zone of the DB instance. timezone is currently only supported by Microsoft SQL Server. The timezone can only be set on creation. See [MSSQL User Guide](http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/CHAP_SQLServer.html#SQLServer.Concepts.General.TimeZone) for more information."
  default     = null
}

variable "timeouts" {
  type = object({
    create = string
    update = string
    delete = string
  })
  description = "A list of DB timeouts to apply to the running code while creating, updating, or deleting the DB instance."
  default = {
    create = "40m"
    update = "80m"
    delete = "60m"
  }
}
