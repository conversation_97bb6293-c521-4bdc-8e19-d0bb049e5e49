terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "remote" {
    organization = "funkylab"

    workspaces {
      name = "Nkmmd_Shared"
    }
  }
}

provider "aws" {
  region = "us-east-1"
}

module "shared" {
  source   = "./shared"
  app_name = var.app_name
}

resource "tls_private_key" "dev-private-key" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

resource "tls_private_key" "prod-private-key" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

resource "aws_key_pair" "dev-key-pair" {
  key_name   = "${var.app_name}_dev"
  public_key = tls_private_key.dev-private-key.public_key_openssh
}

resource "aws_key_pair" "prod-key-pair" {
  key_name   = "${var.app_name}_prod"
  public_key = tls_private_key.prod-private-key.public_key_openssh
}

locals {
  private_key_pem_dev_path  = "../../../ci/nkmmd_dev.pem"
  private_key_pem_prod_path = "../../../ci/nkmmd_prod.pem"
}

resource "null_resource" "save_private_key_dev" {
  provisioner "local-exec" {
    command = "echo '${tls_private_key.dev-private-key.private_key_pem}'"
  }
}
resource "null_resource" "save_private_key_prod" {
  provisioner "local-exec" {
    command = "echo '${tls_private_key.prod-private-key.private_key_pem}'"
  }
}

output "private_key_pem_dev" {
  value     = tls_private_key.dev-private-key.private_key_pem
  sensitive = true
}

output "private_key_pem_prod" {
  value     = tls_private_key.prod-private-key.private_key_pem
  sensitive = true
}