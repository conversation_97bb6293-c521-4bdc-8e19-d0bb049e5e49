terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "remote" {
    organization = "funkylab"

    workspaces {
      name = "Nkmfd_Dev2_Env"
    }
  }
}

provider "aws" {
  region = "us-east-1"
}

module "shared" {
  source   = "./shared"
  app_name = "${var.app_name}_2"
}

data "aws_vpc" "default" {
  default = true
}

locals {
  ami_name = "amazon-docker-server-nakama-${var.app_name}-dev"
  key_name = "${var.app_name}_dev"
}

data "aws_ami" "docker-server" {
  most_recent = true
  owners = [
    var.ami_owner
  ]
  filter {
    name = "name"
    values = [
      local.ami_name
    ]
  }
}

resource "aws_instance" "app_dev_env" {
  ami           = data.aws_ami.docker-server.id
  instance_type = var.instance_type

  vpc_security_group_ids = [
    module.shared.sg_allow_ssh_id,
    module.shared.sg_allow_tls_id,
    module.shared.sg_allow_service_id
  ]

  user_data = data.template_file.user_data.rendered
  key_name  = local.key_name
  tags = {
    Name    = "${var.app_name}_dev2"
    Service = "game"
    App     = var.app_name
    Env     = "dev"
  }

  root_block_device {
    volume_size = 100                      # 指定 EBS 存储大小为 100GB
    volume_type = "gp3"                   # EBS 卷的类型 (可选：gp2, gp3, io1, io2, st1, sc1)
  }

  /*
  The credit_specification block supports the following:
  cpu_credits - (Optional) The credit option for CPU usage. Can be "standard" or "unlimited".
  T3 instances are launched as unlimited by default. T2 instances are launched as standard by default.
  */
  credit_specification {
    cpu_credits = var.cpu_credits_type
  }
}

data "template_file" "user_data" {
  template = file("user-data-game.sh")
  vars = {
    game_service_version = var.game_service_version
  }
}

resource "aws_eip" "dev_ip" {
  domain   = "vpc"
  instance = aws_instance.app_dev_env.id
}

/* seem like change the ip TF cannot recognise, need to modify manually */
resource "aws_route53_record" "app_dev_game_record" {
  zone_id = module.shared.zone_id
  name    = "dev2.${var.app_name}.${module.shared.zone_name}"
  type    = "A"
  ttl     = "300"
  records = [
    aws_eip.dev_ip.public_ip
  ]
  allow_overwrite = true
}

output "ip" {
  value = aws_eip.dev_ip.public_ip
}

//noinspection HttpUrlsUsage
output "app_nakama_console_url" {
  value = "http://${aws_route53_record.app_dev_game_record.name}:7351"
}