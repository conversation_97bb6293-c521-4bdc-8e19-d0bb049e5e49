#!/bin/bash
echo "Start to run user-data.sh "

echo " =========================================="
echo " ====================Game=================="
echo " =========================================="
pwd
cd /home/<USER>
who

echo " =========================================="
echo " Start to launch aws cloudwatch agent"
echo " =========================================="
#/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -s -c file:/home/<USER>/aws_cloudwatch_agent_config.json
#/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a status -m ec2 -s -c file:/home/<USER>/aws_cloudwatch_agent_config.json

sudo apt install postgresql-client-16

echo " =========================================="
echo " Start to add some scripts"
echo " =========================================="
echo "#!/bin/bash" > /home/<USER>/db_dump.sh
echo "time pg_dump -h nkmfd.rds.prod.pwglab.com -p 5432 -U nkmfd_admin -Fc -b -v -f nkmfd-2024-12-11-first.sql -d nkmfd_db" >> /home/<USER>/db_dump.sh
echo "time pg_dump -h nkmfd.rds.prod.pwglab.com -p 5432 -U nkmfd_admin -Fc -b -v -f nkmfd-2024-12-11-second.sql -d nkmfd_db  --inserts --on-conflict-do-nothing" >> /home/<USER>/db_dump.sh
echo "aws s3 cp nkmfd-2024-12-11-first.sql s3://nkmfd-dump --region us-east-1" >> /home/<USER>/db_dump.sh
echo "aws s3 ls s3://nkmfd-dump --region us-east-1" >> /home/<USER>/db_dump.sh


app_name="nkmfd"

#su ec2-user -c "aws s3 cp \"s3://nakama-build/$app_name/composer/dev2/docker-compose.yml\" \"docker-compose.yml\""
#su ec2-user -c "aws s3 cp s3://nakama-build/$app_name/composer/dev2/data/ data/ --recursive"

#sudo service docker status
#sudo service docker start
#sudo service docker status
#whoami
#su ec2-user -c "./docker-login.sh && source .bashrc && export NAKAMA_BACKEND_TAG=${game_service_version} && docker compose up -d"
# clean up the env data
#su ec2-user -c "sed -i "/export COCKROACH_DB_CLOUD_PROD_.*=.*/d" ~/.bashrc"
#su ec2-user -c "sed -i "/export RDS_POSTGRESQL_PROD_.*=.*/d" ~/.bashrc"
#su ec2-user -c "sed -i "/export CR_PAT=.*/d" ~/.bashrc"
#
# wait a while for the log file creation
#sleep 10
#node=0
#
#su ec2-user -c "chmod +x loggly_monitor_log.sh && source .bashrc && ./loggly_monitor_log.sh $app_name-$node"