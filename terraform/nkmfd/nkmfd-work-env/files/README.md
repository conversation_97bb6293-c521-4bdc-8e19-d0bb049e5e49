aws           db_dump.sh  dump2.sh                       nkmfd-2024-12-11-first.sql           nkmfd_dump2_2024-12-12_14-47-20.sql  nohup.out
awscliv2.zip  dump1.sh    nkmfd_2024-12-10_10-02-42.sql  nkmfd_dump1_2024-12-11_09-17-32.sql  nkmfd_dump2_2024-12-16_09-47-46.sql  restore1.sh


主要用于 dump 数据库和上传 s3

文件在这个文件夹中:
[files](../../../../nakama_cloud_db)


```bash
nohup bash dump1.sh &
nohup bash dump2.sh &

# 查看进度
tail -f nohup.out
# 查看结果
cat nohup.out 
```

cat ~/.aws/credentials
```txt
[default]
aws_access_key_id = ********************
aws_secret_access_key = guOfSMo62Sfm16TobbcGLll52xSBNW6LRHel6IjB
```