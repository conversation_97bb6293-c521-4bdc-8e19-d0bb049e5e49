terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "remote" {
    organization = "funkylab"

    workspaces {
      name = "Nkmfd_Prod_RDS"
    }
  }
}

provider "aws" {
  region = "us-east-1"
}

data "aws_vpc" "default" {
  default = true
}

#data "aws_subnet_ids" "default" {
#  vpc_id = data.aws_vpc.default.id
#}
# https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/subnets
data "aws_subnets" "default" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
}

module "rds_instance" {
  source = "cloudposse/rds/aws"
  # Cloud Posse recommends pinning every module to a specific version
  # https://registry.terraform.io/modules/cloudposse/rds/aws/latest
  version = "1.1.0"

  subnet_ids = data.aws_subnets.default.ids
  vpc_id     = data.aws_vpc.default.id

  namespace = local.namespace
  stage     = "prod"
  name      = local.app_name

  dns_zone_id          = var.dns_zone_id
  host_name            = local.host_name
  security_group_ids   = var.security_group_ids
  ca_cert_identifier   = var.ca_cert_identifier
  allowed_cidr_blocks  = local.allowed_cidr_blocks
  database_name        = local.database_name
  database_user        = local.database_user
  database_password    = var.database_password
  database_port        = var.database_port
  multi_az             = var.multi_az
  storage_type         = var.storage_type
  allocated_storage    = var.allocated_storage
  storage_encrypted    = var.storage_encrypted
  engine               = var.engine
  engine_version       = var.engine_version
  major_engine_version = var.major_engine_version
  instance_class       = var.instance_class
  db_parameter_group   = var.db_parameter_group
  #  option_group_name           = var.option_group_name
  publicly_accessible          = var.publicly_accessible
  snapshot_identifier          = var.snapshot_identifier
  auto_minor_version_upgrade   = var.auto_minor_version_upgrade
  allow_major_version_upgrade  = var.allow_major_version_upgrade
  apply_immediately            = var.apply_immediately
  maintenance_window           = var.maintenance_window
  skip_final_snapshot          = var.skip_final_snapshot
  copy_tags_to_snapshot        = var.copy_tags_to_snapshot
  backup_retention_period      = var.backup_retention_period
  backup_window                = var.backup_window
  associate_security_group_ids = local.associate_security_group_ids
  max_allocated_storage        = var.max_allocated_storage
  #
  #  db_parameter = [
  #    { name = "myisam_sort_buffer_size", value = "1048576" },
  #    { name = "sort_buffer_size", value = "2097152" }
  #  ]

  #  db_options = [
  #    {
  #      option_name     = "MARIADB_AUDIT_PLUGIN"
  #      option_settings = [
  #        { name = "SERVER_AUDIT_EVENTS", value = "CONNECT" },
  #        { name = "SERVER_AUDIT_FILE_ROTATIONS", value = "37" }
  #      ]
  #    }
  #  ]
}

/*
return the DB_ADDRESS which can be used for Nakama to connect
this db address should be set to the GitHub Action Secret and put into the AMI image
*/
output "app_rds_db_address" {
  /* [DB_USER]:[DB_PWD]@[HOST]:[POST]/[DB_NAME] */
  value = "${local.database_user}:[PWD]@${local.host_name}.${module.shared.zone_name}:${var.database_port}/${local.database_name}"
}