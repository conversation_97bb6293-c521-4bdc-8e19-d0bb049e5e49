terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "remote" {
    organization = "funkylab"

    workspaces {
      name = "Nkmfd_Prod_Env_Node_0_Three"
    }
  }
}

provider "aws" {
  region = "us-east-1"
}

module "shared" {
  source   = "./shared"
  app_name = var.app_name
}

data "aws_vpc" "default" {
  default = true
}

locals {
  ami_name = "amazon-docker-server-nakama-${var.app_name}-live"
  key_name = "${var.app_name}_prod"
}

data "aws_ami" "docker-server" {
  most_recent = true
  owners = [
    var.ami_owner
  ]
  filter {
    name = "name"
    values = [
      local.ami_name
    ]
  }
}

resource "aws_instance" "app_prod_env" {
  ami           = data.aws_ami.docker-server.id
  instance_type = var.instance_type

  vpc_security_group_ids = [
    module.shared.app_shared_allow_ssh,
    module.shared.app_shared_whitelist,
    /* game api from everywhere */
    module.shared.app_shared_allow_all_api,
    /* whitelist to console */
    module.shared.app_shared_whitelist_http_console,
    /* connect to RDS */
    module.shared.app_ec2_to_rds_outbound_for_ec2,
  ]

  user_data = data.template_file.user_data.rendered
  key_name  = local.key_name
  tags = {
    Name    = "${var.app_name}_prod_node_${var.node_index}${var.suffix}"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
    Node    = var.node_index
  }
  /*
  The credit_specification block supports the following:
  cpu_credits - (Optional) The credit option for CPU usage. Can be "standard" or "unlimited".
  T3 instances are launched as unlimited by default. T2 instances are launched as standard by default.
  */
  credit_specification {
    cpu_credits = var.cpu_credits_type
  }
}

data "template_file" "user_data" {
  template = file("user-data-game.sh")
  vars = {
    game_service_version = var.game_service_version
    use_rds_db           = var.use_rds_db
    node_index           = var.node_index
  }
}

resource "aws_eip" "prod_ip" {
  domain   = "vpc"
  instance = aws_instance.app_prod_env.id
}

/* seem like change the ip TF cannot recognise, need to modify manually */
resource "aws_route53_record" "app_prod_game_record" {
  zone_id = module.shared.zone_id
  name    = "prod-node-${var.node_index}${var.suffix}.${var.app_name}.${module.shared.zone_name}"
  type    = "A"
  ttl     = "300"
  records = [
    aws_eip.prod_ip.public_ip
  ]
  allow_overwrite = true
}

output "ip" {
  value = aws_eip.prod_ip.public_ip
}

//noinspection HttpUrlsUsage
output "app_nakama_console_url" {
  value = "http://${aws_route53_record.app_prod_game_record.name}:7351"
}