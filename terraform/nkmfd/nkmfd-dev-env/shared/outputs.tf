output "sg_allow_ssh" {
  value = aws_security_group.app_dev_allow_ssh.name
}

output "sg_allow_tls" {
  value = aws_security_group.app_dev_allow_tls.name
}
output "sg_allow_service" {
  value = aws_security_group.app_dev_allow_service.name
}

output "sg_allow_ssh_id" {
  value = aws_security_group.app_dev_allow_ssh.id
}

output "sg_allow_tls_id" {
  value = aws_security_group.app_dev_allow_tls.id
}
output "sg_allow_service_id" {
  value = aws_security_group.app_dev_allow_service.id
}

output "zone_id" {
  value = data.aws_route53_zone.selected.zone_id
}

output "zone_name" {
  value = data.aws_route53_zone.selected.name
}

output "app_name" {
  value = var.app_name
}