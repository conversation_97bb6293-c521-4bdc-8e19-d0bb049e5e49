#!/bin/bash
echo "Start to run user-data.sh "

echo " =========================================="
echo " ====================Game=================="
echo " =========================================="
pwd
cd /home/<USER>
who

echo " =========================================="
echo " Start to launch aws cloudwatch agent"
echo " =========================================="
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -s -c file:/home/<USER>/aws_cloudwatch_agent_config.json
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a status -m ec2 -s -c file:/home/<USER>/aws_cloudwatch_agent_config.json

app_name="nkmfd"

su ec2-user -c "aws s3 cp \"s3://nakama-build/$app_name/composer/prod/docker-compose.yml\" \"docker-compose.yml\""
#su ec2-user -c "aws s3 cp s3://nakama-build/$app_name/composer/prod/data/ data/ --recursive"

sudo service docker status
sudo service docker start
sudo service docker status
whoami

su ec2-user -c "chmod +x db_address_switch.sh && source .bashrc && ./db_address_switch.sh ${use_rds_db} && source .bashrc && ./docker-login.sh && export NAKAMA_BACKEND_TAG=${game_service_version} && export NAKAMA_PROD_NODE_ID=${node_index} && cat .bashrc > .bashrc_copy && source .bashrc && docker compose up -d"
#               chmod +x db_address_switch.sh && source .bashrc && ./db_address_switch.sh 0             && source .bashrc && ./docker-login.sh && export NAKAMA_BACKEND_TAG=1.0.5                   && docker compose up -d
sleep 10
node=${node_index}

su ec2-user -c "chmod +x loggly_monitor_log.sh && source .bashrc && ./loggly_monitor_log.sh $app_name-$node"