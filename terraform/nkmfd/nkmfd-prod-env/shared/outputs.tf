output "zone_id" {
  value = data.aws_route53_zone.selected.zone_id
}

output "zone_name" {
  value = data.aws_route53_zone.selected.name
}

output "app_name" {
  value = var.app_name
}

output "app_shared_allow_ssh" {
  value = data.aws_security_group.app_shared_allow_ssh.id
}

output "app_shared_whitelist" {
  value = data.aws_security_group.app_shared_whitelist.id
}

output "app_shared_allow_tls" {
  value = data.aws_security_group.app_shared_allow_tls.id
}

output "app_shared_allow_http_api" {
  value = data.aws_security_group.app_shared_allow_http_api.id
}

output "app_shared_allow_grpc_api" {
  value = data.aws_security_group.app_shared_allow_grpc_api.id
}

output "app_shared_allow_all_api" {
  value = data.aws_security_group.app_shared_allow_all_api.id
}

output "app_shared_whitelist_http_console" {
  value = data.aws_security_group.app_shared_whitelist_http_console.id
}

output "app_shared_allow_service" {
  value = data.aws_security_group.app_shared_allow_service.id
}

output "app_rds_to_ec2_inbound_for_rds" {
  value = data.aws_security_group.app_rds_to_ec2_inbound_for_rds.id
}

output "app_ec2_to_rds_outbound_for_ec2" {
  value = data.aws_security_group.app_ec2_to_rds_outbound_for_ec2.id
}