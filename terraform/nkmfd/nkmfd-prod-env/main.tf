terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "remote" {
    organization = "funkylab"

    workspaces {
      name = "Nkmfd_Prod_Env"
    }
  }
}

provider "aws" {
  region = "us-east-1"
}

module "shared" {
  source   = "./shared"
  app_name = var.app_name
}

data "aws_vpc" "default" {
  default = true
}
// Subnet_id's in the order of Availability_zones
// https://github.com/hashicorp/terraform-provider-aws/issues/2120
// https://github.com/hashicorp/terraform-provider-aws/issues/3471
data "aws_subnet" "private" {
  count = length(data.aws_subnets.default.ids)
  id    = tolist(data.aws_subnets.default.ids)[count.index]
}

locals {
  ids_sorted_by_az  = values(zipmap(data.aws_subnet.private.*.availability_zone, data.aws_subnet.private.*.id))
  cidr_sorted_by_az = values(zipmap(data.aws_subnet.private.*.availability_zone, data.aws_subnet.private.*.cidr_block))
}

locals {
  game_prod_version_display = replace(var.game_service_version, ".", "_")
  // we can increase the available zones to increase the High Available
  //  we'd better to have at least one instance in each available zone, other wise the Load balance will be slow
  //   https://superuser.com/a/1210792/579654
  selected_available_zones = [
    element(tolist(local.ids_sorted_by_az), 0),
    element(tolist(local.ids_sorted_by_az), 1),
    element(tolist(local.ids_sorted_by_az), 2)
  ]

  nlb_name = "${var.app_name}-prod${var.suffix}"
  ami_name = "amazon-docker-server-nakama-${var.app_name}"
  key_name = "${var.app_name}_prod"
}



# this can be used to query ami image id by condition without hard code ami id.
data "aws_ami" "docker-server" {
  most_recent = true
  owners = [
    var.ami_owner
  ]
  filter {
    name = "name"
    values = [
      local.ami_name
    ]
  }
}

resource "aws_iam_instance_profile" "app_prod_ec2_profile" {
  name = "${var.app_name}_prod_ec2${var.suffix}"
  role = aws_iam_role.app_prod_ec2_role.name
}

resource "aws_iam_role" "app_prod_ec2_role" {
  name = "${var.app_name}_prod_ec2_role${var.suffix}"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": {
        "Service": [
          "ec2.amazonaws.com"
        ]
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

/**
Launch Template can be edited, so it will only be created one time. so need not use the lifecycle with create_before_destroy*/
resource "aws_launch_template" "app_prod" {
  name                   = "${var.app_name}_prod${var.suffix}"
  description            = local.game_prod_version_display
  image_id               = data.aws_ami.docker-server.id
  instance_type          = var.instance_type
  update_default_version = true
  vpc_security_group_ids = [
    module.shared.app_shared_allow_ssh,
    module.shared.app_shared_whitelist,
    /* game api from everywhere */
    module.shared.app_shared_allow_all_api,
    /* whitelist to console */
    module.shared.app_shared_allow_service,
    /* connect to RDS */
    module.shared.app_ec2_to_rds_outbound_for_ec2,
  ]
  iam_instance_profile {
    name = aws_iam_instance_profile.app_prod_ec2_profile.name
  }

  user_data = base64encode(data.template_file.user_data_game_server.rendered)
  // user_data = data.template_file.user_data_game_server.rendered
  key_name = local.key_name

  credit_specification {
    cpu_credits = var.cpu_credits_type
  }
}

resource "aws_autoscaling_group" "app_prod" {
  name = "${var.app_name}_prod${var.suffix}"
  launch_template {
    id      = aws_launch_template.app_prod.id
    version = "$Latest"
  }
  vpc_zone_identifier = local.selected_available_zones

  target_group_arns = [
    aws_lb_target_group.target_group_7349.arn,
    aws_lb_target_group.target_group_7350.arn,
    aws_lb_target_group.target_group_7351.arn
  ]
  health_check_type = "ELB"

  desired_capacity = 1
  min_size         = 1
  max_size         = 1

  // https://registry.terraform.io/providers/hashicorp/aws/latest/docs/guides/version-5-upgrade#resourceaws_autoscaling_group
  // Remove tags from configurations as it no longer exists. Use the tag attribute instead. For use cases requiring dynamic tags, see the Dynamic Tagging example.

  tag {
    key                 = "Name"
    value               = "${var.app_name}_prod_${local.game_prod_version_display}${var.suffix}"
    propagate_at_launch = true
  }

  tag {
    key                 = "Service"
    value               = "game"
    propagate_at_launch = true
  }

  tag {
    key                 = "App"
    value               = var.app_name
    propagate_at_launch = true
  }

  tag {
    key                 = "Env"
    value               = "Prod"
    propagate_at_launch = true
  }
}

resource "aws_lb" "app_prod_nlb" {
  name               = local.nlb_name
  ip_address_type    = "dualstack"
  load_balancer_type = "network"
  subnets            = local.selected_available_zones
  //    security_groups = [
  //        module.shared.sg_allow_ssh,
  //        module.shared.sg_allow_tls,
  //        module.shared.sg_allow_service,
  //        module.shared.sg_allow_redis
  //    ]
  tags = {
    Name    = "${var.app_name}_prod_nlb${var.suffix}"
    Service = "Game"
    Type    = "Prod"
  }
}
/*7349*/
resource "aws_lb_target_group" "target_group_7349" {
  name                 = "${local.nlb_name}-7349${var.suffix}"
  port                 = 7349
  protocol             = "TCP"
  vpc_id               = data.aws_vpc.default.id
  target_type          = "instance"
  deregistration_delay = 300
  health_check {
    interval            = 30
    port                = 7349
    protocol            = "TCP"
    healthy_threshold   = 7
    unhealthy_threshold = 7
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }

  tags = {
    Name    = "${local.nlb_name}_game_lb_tg_port_7349${var.suffix}"
    Env     = "Prod"
    App     = var.app_name
    Service = "Game"
  }
}

resource "aws_lb_listener" "listener_port_7349" {
  load_balancer_arn = aws_lb.app_prod_nlb.arn
  port              = 7349
  protocol          = "TCP"
  default_action {
    target_group_arn = aws_lb_target_group.target_group_7349.arn
    type             = "forward"
  }
}
/*7350*/
resource "aws_lb_target_group" "target_group_7350" {
  name                 = "${local.nlb_name}-7350${var.suffix}"
  port                 = 7350
  protocol             = "TCP"
  vpc_id               = data.aws_vpc.default.id
  target_type          = "instance"
  deregistration_delay = 300
  health_check {
    interval            = 30
    port                = 7350
    protocol            = "HTTP"
    healthy_threshold   = 7
    unhealthy_threshold = 7
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }

  tags = {
    Name    = "${local.nlb_name}_game_lb_tg_port_7350${var.suffix}"
    Env     = "Prod"
    App     = var.app_name
    Service = "Game"
  }
}

resource "aws_lb_listener" "listener_port_7350" {
  load_balancer_arn = aws_lb.app_prod_nlb.arn
  port              = 7350
  protocol          = "TCP"
  default_action {
    target_group_arn = aws_lb_target_group.target_group_7350.arn
    type             = "forward"
  }
}
/*7351*/
resource "aws_lb_target_group" "target_group_7351" {
  name                 = "${local.nlb_name}-7351${var.suffix}"
  port                 = 7351
  protocol             = "TCP"
  vpc_id               = data.aws_vpc.default.id
  target_type          = "instance"
  deregistration_delay = 300
  health_check {
    interval            = 30
    port                = 7351
    protocol            = "HTTP"
    healthy_threshold   = 7
    unhealthy_threshold = 7
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }

  tags = {
    Name    = "${local.nlb_name}_game_lb_tg_port_7351${var.suffix}"
    Env     = "Prod"
    App     = var.app_name
    Service = "Game"
  }
}

resource "aws_lb_listener" "listener_port_7351" {
  load_balancer_arn = aws_lb.app_prod_nlb.arn
  port              = 7351
  protocol          = "TCP"
  default_action {
    target_group_arn = aws_lb_target_group.target_group_7351.arn
    type             = "forward"
  }
}

data "template_file" "user_data_game_server" {
  template = file("user-data-game.sh")

  vars = {
    game_service_version = var.game_service_version
    use_rds_db           = var.use_rds_db
  }
}

#data "aws_subnet_ids" "default" {
#    vpc_id = data.aws_vpc.default.id
#}

data "aws_subnets" "default" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
}

resource "aws_route53_record" "app_prod_game_record_unified" {
  zone_id = module.shared.zone_id
  name    = "${var.live_game_subdomain}.${module.shared.app_name}.${module.shared.zone_name}"
  type    = "CNAME"
  ttl     = "300"
  records = [
    aws_lb.app_prod_nlb.dns_name
  ]
  allow_overwrite = true
}

resource "aws_route53_record" "app_prod_game_record" {
  zone_id = module.shared.zone_id
  name    = "${var.live_game_subdomain}${var.suffix}.${module.shared.app_name}.${module.shared.zone_name}"
  type    = "CNAME"
  ttl     = "300"
  records = [
    aws_lb.app_prod_nlb.dns_name
  ]
  allow_overwrite = true
}

#resource "aws_route53_record" "app_prod_game_record_node_1" {
#  zone_id = module.shared.zone_id
#  name    = "${var.live_game_subdomain}.node1.${module.shared.app_name}.${module.shared.zone_name}"
#  type    = "CNAME"
#  ttl     = "300"
#  records = [
#    aws_lb.app_prod_nlb.dns_name
#  ]
#  allow_overwrite = true
#}
#
#resource "aws_route53_record" "app_prod_game_record_node_2" {
#  zone_id = module.shared.zone_id
#  name    = "${var.live_game_subdomain}.node2.${module.shared.app_name}.${module.shared.zone_name}"
#  type    = "CNAME"
#  ttl     = "300"
#  records = [
#    aws_lb.app_prod_nlb.dns_name
#  ]
#  allow_overwrite = true
#}

/*
Add 30 percent of group
Add capacity units in increments of at least 1 capacity units

And then wait:
300 seconds before allowing another scaling activity
*/
resource "aws_autoscaling_policy" "app_prod_cpu_percentage_scala_up" {
  name = "${aws_autoscaling_group.app_prod.name}_cpu_percentage_scala_up"
  //The number of instances by which to scale.
  scaling_adjustment = 30
  adjustment_type    = "PercentChangeInCapacity"
  // Minimum value to scale by when adjustment_type is set to PercentChangeInCapacity.
  min_adjustment_magnitude = 1
  cooldown                 = 300
  autoscaling_group_name   = aws_autoscaling_group.app_prod.name
}

resource "aws_cloudwatch_metric_alarm" "app_prod_cpu_percentage_scala_up_alarm" {
  alarm_name          = "${aws_autoscaling_group.app_prod.name}_cpu_percentage_scala_up_alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  // (Required) The number of periods over which data is compared to the specified threshold.
  evaluation_periods  = "5"
  datapoints_to_alarm = "5"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  // (Optional) The period in seconds over which the specified statistic is applied.
  period    = "60"
  statistic = "Average"
  threshold = "60"
  unit      = "Percent"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.app_prod.name
  }

  alarm_description = "Scala up (Percentage) when CPUUtilization >= 60 for 5 datapoints within 5 minutes"
  alarm_actions = [
  aws_autoscaling_policy.app_prod_cpu_percentage_scala_up.arn]
}

/*
spikeUpId=$(aws autoscaling put-scaling-policy --policy-name spikeScaleUpPolicy --auto-scaling-group-name $scalingGroupName --scaling-adjustment 30 --adjustment-type PercentChangeInCapacity --min-adjustment-magnitude 1 --region $region | egrep -m 1 -o 'arn[^ ]+')

aws cloudwatch put-metric-alarm --alarm-name ${scalingGroupName}${alarmScaleUp}2 --metric-name CPUUtilization --namespace AWS/EC2 --statistic Average --period 60 --threshold 85 --comparison-operator GreaterThanOrEqualToThreshold  --dimensions "Name=AutoScalingGroupName,Value=$scalingGroupName" --evaluation-periods 2 --unit Percent --alarm-actions $spikeUpId --region $region
*/
/*
breaches the alarm threshold: mem_used_percent >= 85 for 10 consecutive periods of 60 seconds for the metric dimensions:
AutoScalingGroupName =

Take the action:
Add 30 percent of group
Add capacity units in increments of at least 1 capacity units

And then wait:
300 seconds before allowing another scaling activity
*/
resource "aws_autoscaling_policy" "app_prod_cpu_spike_scala_up" {
  name = "${aws_autoscaling_group.app_prod.name}_cpu_spike_scala_up"
  //The number of instances by which to scale.
  scaling_adjustment = 30
  adjustment_type    = "PercentChangeInCapacity"
  // Minimum value to scale by when adjustment_type is set to PercentChangeInCapacity.
  min_adjustment_magnitude = 1
  cooldown                 = 300
  autoscaling_group_name   = aws_autoscaling_group.app_prod.name
}

resource "aws_cloudwatch_metric_alarm" "app_prod_cpu_spike_scala_up_alarm" {
  alarm_name          = "${aws_autoscaling_group.app_prod.name}_cpu_spike_scala_up_alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  // (Required) The number of periods over which data is compared to the specified threshold.
  evaluation_periods  = "2"
  datapoints_to_alarm = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  // (Optional) The period in seconds over which the specified statistic is applied.
  period    = "60"
  statistic = "Average"
  threshold = "85"
  unit      = "Percent"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.app_prod.name
  }

  alarm_description = "Scala up (Spike) when CPUUtilization >= 85 for 2 datapoints within 2 minutes"
  alarm_actions = [
  aws_autoscaling_policy.app_prod_cpu_spike_scala_up.arn]
}

/*
scaleDownId=$(aws autoscaling put-scaling-policy --policy-name percentageScaleDownPolicy --auto-scaling-group-name $scalingGroupName --scaling-adjustment -1 --adjustment-type ChangeInCapacity --region $region | egrep -m 1 -o 'arn[^ ]+')

alarmScaleDown='AlarmScaleDown'
aws cloudwatch put-metric-alarm --alarm-name $scalingGroupName$alarmScaleDown --metric-name CPUUtilization --namespace AWS/EC2 --statistic Average --period 300 --threshold 30 --comparison-operator LessThanOrEqualToThreshold  --dimensions "Name=AutoScalingGroupName,Value=$scalingGroupName" --evaluation-periods 2 --unit Percent --alarm-actions $scaleDownId --region $region
*/
/*
breaches the alarm threshold: CPUUtilization =< 30 for 2 consecutive periods of 300 seconds for the metric dimensions:
AutoScalingGroupName =

Take the action:
Remove 1 capacity units

And then wait:
300 seconds before allowing another scaling activity
*/
resource "aws_autoscaling_policy" "app_prod_cpu_percentage_scala_down" {
  name = "${aws_autoscaling_group.app_prod.name}_cpu_percentage_scala_down"
  //The number of instances by which to scale.
  scaling_adjustment     = -1
  adjustment_type        = "ChangeInCapacity"
  cooldown               = 300
  autoscaling_group_name = aws_autoscaling_group.app_prod.name
}

resource "aws_cloudwatch_metric_alarm" "app_prod_cpu_percentage_scala_down_alarm" {
  alarm_name          = "${aws_autoscaling_group.app_prod.name}_cpu_percentage_scala_down_alarm"
  comparison_operator = "LessThanOrEqualToThreshold"
  // (Required) The number of periods over which data is compared to the specified threshold.
  evaluation_periods  = "2"
  datapoints_to_alarm = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  // (Optional) The period in seconds over which the specified statistic is applied.
  period    = "300"
  statistic = "Average"
  threshold = "30"
  unit      = "Percent"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.app_prod.name
  }
  // so the 2 datapoints: datapoints_to_alarm = 2, and the 10 mins: evaluation_periods * period
  alarm_description = "Scala down (Percentage) CPUUtilization <= 30 for 2 datapoints within 10 minutes."
  alarm_actions = [
  aws_autoscaling_policy.app_prod_cpu_percentage_scala_down.arn]
}


/*
memoryScaleUpId=$(aws autoscaling put-scaling-policy --policy-name percentageMemoryScaleUpPolicy --auto-scaling-group-name $scalingGroupName --scaling-adjustment 30 --adjustment-type PercentChangeInCapacity --min-adjustment-magnitude 1 --region $region | egrep -m 1 -o 'arn[^ ]+')

memoryAlarmScaleUp='MemoryAlarmScaleUp'
aws cloudwatch put-metric-alarm --alarm-name $scalingGroupName$memoryAlarmScaleUp --metric-name mem_used_percent --namespace CWAgent --statistic Average --period 60 --threshold 85 --comparison-operator GreaterThanOrEqualToThreshold  --dimensions "Name=AutoScalingGroupName,Value=$scalingGroupName" --evaluation-periods 10 --unit Percent --alarm-actions $memoryScaleUpId --region $region
*/
/*
breaches the alarm threshold: mem_used_percent >= 85 for 10 consecutive periods of 60 seconds for the metric dimensions:
AutoScalingGroupName =

Take the action:
Add 30 percent of group
Add capacity units in increments of at least 1 capacity units
And then wait:
300 seconds before allowing another scaling activity

*/
resource "aws_autoscaling_policy" "app_prod_mem_percentage_scala_up" {
  name = "${aws_autoscaling_group.app_prod.name}_mem_percentage_scala_up"
  //The number of instances by which to scale.
  scaling_adjustment = 30
  adjustment_type    = "PercentChangeInCapacity"
  // Minimum value to scale by when adjustment_type is set to PercentChangeInCapacity.
  min_adjustment_magnitude = 1
  cooldown                 = 300
  autoscaling_group_name   = aws_autoscaling_group.app_prod.name
}

resource "aws_cloudwatch_metric_alarm" "app_prod_mem_percentage_scala_up_alarm" {
  alarm_name          = "${aws_autoscaling_group.app_prod.name}_mem_percentage_scala_up_alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  // (Required) The number of periods over which data is compared to the specified threshold.
  evaluation_periods  = "10"
  datapoints_to_alarm = "10"
  metric_name         = "mem_used_percent"
  namespace           = "CWAgent"
  // (Optional) The period in seconds over which the specified statistic is applied.
  period    = "60"
  statistic = "Average"
  threshold = "85"
  unit      = "Percent"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.app_prod.name
  }
  // so the 2 datapoints: datapoints_to_alarm = 2, and the 10 mins: evaluation_periods * period
  alarm_description = "Scala up (Percentage) mem_used_percent >= 85 for 10 datapoints within 10 minutes."
  alarm_actions = [
  aws_autoscaling_policy.app_prod_mem_percentage_scala_up.arn]
}

output "lb_dns_name" {
  value = aws_lb.app_prod_nlb.dns_name
}

output "target_group_7349_arn" {
  value = aws_lb_target_group.target_group_7349.arn
}

output "target_group_7350_arn" {
  value = aws_lb_target_group.target_group_7350.arn
}

output "target_group_7351_arn" {
  value = aws_lb_target_group.target_group_7351.arn
}

output "app_name" {
  value = var.app_name
}

output "hosted_zone_id" {
  value = module.shared.zone_id
}
