# 步骤
# 1. init
# 2. 添加 aws credential, 查看 sh 脚本
# 3. plan

tf-init:
	terraform init

tf-format:
	terraform fmt -recursive

tf-plan:
	terraform plan

tf-apply: tf-format
	terraform apply

tf-destroy:
	terraform destroy

tf-export-pem-dev:
	terraform output -raw private_key_pem_dev > ../../../ci/nkmad_dev.pem

tf-export-pem-prod:
	terraform output -raw private_key_pem_prod > ../../../ci/nkmad_prod.pem

# Cloud Link
# https://app.terraform.io/app/funkylab/workspaces/Nakama_Backend_Shared/variables

tf-mac:
	terraform providers lock -platform=windows_amd64 -platform=darwin_amd64 -platform=linux_amd64
	terraform providers lock -platform=windows_amd64 -platform=darwin_amd64 -platform=linux_amd64

# update the module
tf-tofu-get:
	tofu get