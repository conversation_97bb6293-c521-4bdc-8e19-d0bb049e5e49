
provider "aws" {
    alias = "virginia"
    region = "us-east-1"
}

resource "aws_acm_certificate" "certificate" {
    domain_name       = local.domain_name
    validation_method = "DNS"
    subject_alternative_names = [
        "www.${local.domain_name}"
    ]
    tags = {
        App  = var.game_id
        Env  = var.env
        Type = var.type
        Service = var.external_service
    }

    lifecycle {
        create_before_destroy = true
    }
    /* the Cloudfront ACM certificate must create in us-east-1
    https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/cnames-and-https-requirements.html
    */
    provider = aws.virginia
}

resource "aws_route53_record" "certificate_validation" {
    for_each = {
        for dvo in aws_acm_certificate.certificate.domain_validation_options : dvo.domain_name => {
            name   = dvo.resource_record_name
            record = dvo.resource_record_value
            type   = dvo.resource_record_type
        }
    }

    allow_overwrite = true
    name            = each.value.name
    records         = [each.value.record]
    ttl             = 60
    type            = each.value.type
    zone_id         = data.aws_route53_zone.domain.zone_id
}


resource "aws_acm_certificate_validation" "certificate_validation" {
    certificate_arn         = aws_acm_certificate.certificate.arn
    validation_record_fqdns = [for record in aws_route53_record.certificate_validation : record.fqdn]
    /* the Cloudfront ACM certificate must create in us-east-1
    https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/cnames-and-https-requirements.html
    */
    provider = aws.virginia
}