terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}


provider "aws" {
  region = "us-east-1"  # 你可以根据需要设置区域
}

resource "aws_api_gateway_resource" "webhook_resource" {
  rest_api_id = var.api_gateway_instance_id
  parent_id   = var.parent_rpc_resource_id
  path_part   = var.path_part
}

///////////

resource "aws_api_gateway_method" "webhook_post" {
  rest_api_id   = var.api_gateway_instance_id
  resource_id   = aws_api_gateway_resource.webhook_resource.id
  http_method   = "POST"
  authorization = "NONE"

  # 必须声明这些 headers 作为 method 请求的参数
  request_parameters = {
    "method.request.header.Authorization" : true
    "method.request.header.Content-Type" : true
    "method.request.header.X-Forwarded-For" : true
    #    "method.request.header.X-Requested-With" : false # 可选的 header
  }
}

resource "aws_api_gateway_integration" "webhook_integration" {
  rest_api_id             = var.api_gateway_instance_id
  resource_id             = aws_api_gateway_resource.webhook_resource.id
  http_method             = aws_api_gateway_method.webhook_post.http_method
  integration_http_method = "POST"
  type                    = "HTTP"
  uri                     = "${var.service_http_scheme}://${var.service_url}/v2/rpc/${var.path_part}"

  # 添加 query 参数
  request_parameters = {
    "integration.request.querystring.http_key" = "'${var.http_key}'"
    "integration.request.querystring.unwrap"   = "'${var.unwrap}'"
    "integration.request.header.Authorization" = "method.request.header.Authorization"
    "integration.request.header.Content-Type"  = "method.request.header.Content-Type"
    #   ip 信息
    "integration.request.header.X-Forwarded-For"  = "method.request.header.X-Forwarded-For"
  }

  request_templates = {
    "application/json" = <<EOF
    $input.body
EOF
    /* header 传送的通用方案, 应该也工作:  记住一定要 deploy 之后再测试 */
    /*
        #set($allHeaders = $input.params().header)
    {
      "headers": {
        #foreach($header in $allHeaders.keySet())
          "$header": "$allHeaders.get($header)"#if($foreach.hasNext()),#end
        #end
      },
      "body": $input.body
    }
    EOF

    */
  }
}

# 定义自定义的 400 错误响应
resource "aws_api_gateway_integration_response" "custom_400_response" {
  rest_api_id = var.api_gateway_instance_id
  resource_id = aws_api_gateway_resource.webhook_resource.id
  http_method = aws_api_gateway_method.webhook_post.http_method

  status_code = "400"

  #  response_templates = {
  #    "application/json" = <<EOF
  ##set($inputRoot = $input.path('$'))
  #{
  #  "error": {
  #    "code": "$inputRoot.error.code",
  #    "message": "$inputRoot.error.message"
  #  }
  #}
  #EOF
  #  }

  # 使用 VTL 模板对响应进行重构
  response_templates = {
    "application/json" = <<EOF
    #set($inputRoot = $input.path('$'))
    #set($rawMessage = $inputRoot.message)

    #if($rawMessage)
      #set($parsedMessage = $util.parseJson($rawMessage))
      {
        "code": 400,
        "error": {
          "code": "$parsedMessage.error.code",
          "message": "$parsedMessage.error.message"
        }
      }
    #end
    EOF
  }

  selection_pattern = ".*400.*"
  depends_on        = [aws_api_gateway_integration.webhook_integration]
}

# Integration Response for 200 OK
resource "aws_api_gateway_integration_response" "response_200" {
  rest_api_id = var.api_gateway_instance_id
  resource_id = aws_api_gateway_resource.webhook_resource.id
  http_method = aws_api_gateway_method.webhook_post.http_method
  status_code = "200"

  response_templates = {
    "application/json" = ""
  }
  selection_pattern = ".*200.*"
  depends_on = [aws_api_gateway_integration.webhook_integration]
}

# Integration Response for 500 Internal Server Error
resource "aws_api_gateway_integration_response" "response_500" {
  rest_api_id = var.api_gateway_instance_id
  resource_id = aws_api_gateway_resource.webhook_resource.id
  http_method = aws_api_gateway_method.webhook_post.http_method
  status_code = "500"

  response_templates = {
    "application/json" = ""
  }
  selection_pattern = ".*500.*"
  depends_on = [aws_api_gateway_integration.webhook_integration]
}

resource "aws_api_gateway_method_response" "webhook_400_method_response" {
  rest_api_id = var.api_gateway_instance_id
  resource_id = aws_api_gateway_resource.webhook_resource.id
  http_method = aws_api_gateway_method.webhook_post.http_method
  status_code = "400"

  response_models = {
    "application/json" = "Empty"
  }
}

# Method Response for 200 OK
resource "aws_api_gateway_method_response" "webhook_200_method_response" {
  rest_api_id = var.api_gateway_instance_id
  resource_id = aws_api_gateway_resource.webhook_resource.id
  http_method = aws_api_gateway_method.webhook_post.http_method
  status_code = "200"

  response_models = {
    "application/json" = "Empty"
  }
}

# Method Response for 500 Internal Server Error
resource "aws_api_gateway_method_response" "webhook_500_method_response" {
  rest_api_id = var.api_gateway_instance_id
  resource_id = aws_api_gateway_resource.webhook_resource.id
  http_method = aws_api_gateway_method.webhook_post.http_method
  status_code = "500"

  response_models = {
    "application/json" = "Empty"
  }
}