#!/bin/bash

WORKSPACE_ID=ws-[]
KEY=database_password

# https://github.com/awsdocs/aws-cloudformation-user-guide/blob/c03a45977c5a506e09a22dbe05ff980bec79b805/doc_source/aws-properties-rds-database-instance.md#cfn-rds-dbinstance-masteruserpassword
# MasterUserPassword The password for the master user. The password can include any printable ASCII character except "/", """, or "@".
VALUE=[]
DESCRIPTION=AWS_RDS_database_password
TOKEN=[]
SENSITIVE=true
./add-var-tf.sh "${WORKSPACE_ID}" ${KEY} "${VALUE}" "${DESCRIPTION}" "${TOKEN}" ${SENSITIVE}