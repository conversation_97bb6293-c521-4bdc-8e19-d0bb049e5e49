#!/bin/bash
WORKSPACE_ID=$1
AWS_KEY=$2
AWS_SECRET=$3
AWS_DESCRIPTION=$4
TOKEN=$5

generate_post_data()
{
VAR_KEY=$1
VAR_VALUE=$2
VAR_DESCRIPTION=$3
  cat <<EOF
{
  "data": {
    "type":"vars",
    "attributes": {
      "key":"${VAR_KEY}",
      "value":"${VAR_VALUE}",
      "description":"${VAR_DESCRIPTION}",
      "category":"env",
      "hcl":false,
      "sensitive":true
    },
    "relationships": {
      "workspace": {
        "data": {
          "id":"${WORKSPACE_ID}",
          "type":"workspaces"
        }
      }
    }
  }
}
EOF
}

curl \
  --header "Authorization: Bearer ${TOKEN}" \
  --header "Content-Type: application/vnd.api+json" \
  --request POST \
  --data "$(generate_post_data "AWS_ACCESS_KEY_ID" "${AWS_KEY}" "${AWS_DESCRIPTION}")" \
  https://app.terraform.io/api/v2/vars

curl \
  --header "Authorization: Bearer ${TOKEN}" \
  --header "Content-Type: application/vnd.api+json" \
  --request POST \
  --data "$(generate_post_data "AWS_SECRET_ACCESS_KEY" "${AWS_SECRET}" "${AWS_DESCRIPTION}")" \
  https://app.terraform.io/api/v2/vars