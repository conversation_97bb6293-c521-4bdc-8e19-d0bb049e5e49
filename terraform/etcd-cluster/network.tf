# ==============================================================================
# Networking Resources (VPC, Subnets, Endpoints, Peering, NLB)
# ==============================================================================

resource "aws_vpc" "etcd_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_support   = true
  enable_dns_hostnames = true

  tags = {
    Name = "${var.cluster_name}-vpc"
  }
}

# Create private subnets for the etcd nodes
resource "aws_subnet" "private" {
  count             = var.node_count
  vpc_id            = aws_vpc.etcd_vpc.id
  cidr_block        = "10.0.${count.index}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name = "${var.cluster_name}-private-subnet-${count.index + 1}"
  }
}

# Route table for the private subnets (no route to the internet)
resource "aws_route_table" "private" {
  vpc_id = aws_vpc.etcd_vpc.id
  tags = {
    Name = "${var.cluster_name}-private-rt"
  }
}

resource "aws_route_table_association" "private" {
  count          = var.node_count
  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private.id
}

# S3 Gateway Endpoint: Allows access to S3 from the private subnets (free)
resource "aws_vpc_endpoint" "s3" {
  vpc_id          = aws_vpc.etcd_vpc.id
  service_name    = "com.amazonaws.${var.aws_region}.s3"
  route_table_ids = [aws_route_table.private.id]
}

# EC2 Interface Endpoint: Allows access to EC2 API from the private subnets (paid)
resource "aws_vpc_endpoint" "ec2" {
  vpc_id              = aws_vpc.etcd_vpc.id
  service_name        = "com.amazonaws.${var.aws_region}.ec2"
  vpc_endpoint_type   = "Interface"
  private_dns_enabled = true
  subnet_ids          = aws_subnet.private[*].id
  security_group_ids  = [aws_security_group.vpc_endpoint_sg.id]
}

# VPC Peering connection to the application's VPC
resource "aws_vpc_peering_connection" "etcd_to_default" {
  # Only create if peer_vpc_id is set
  count = var.peer_vpc_id != "" ? 1 : 0

  peer_vpc_id = var.peer_vpc_id
  vpc_id      = aws_vpc.etcd_vpc.id
  auto_accept = false # Requires manual acceptance in the AWS console or via another Terraform

  tags = {
    Name = "Peering: ${var.cluster_name}-vpc to App-VPC"
  }
}

# Add a route to the etcd VPC's private route table pointing to the app's VPC
resource "aws_route" "to_default_vpc" {
  # Only create if peer_vpc_id and its CIDR are set
  count = var.peer_vpc_id != "" && var.peer_vpc_cidr_block != "" ? 1 : 0

  route_table_id            = aws_route_table.private.id
  destination_cidr_block    = var.peer_vpc_cidr_block
  vpc_peering_connection_id = aws_vpc_peering_connection.etcd_to_default[0].id
}


# ==============================================================================
# Network Load Balancer (NLB) for a stable client endpoint
# ==============================================================================

resource "aws_lb" "etcd" {
  count = var.create_nlb ? 1 : 0

  name               = "${var.cluster_name}-nlb"
  internal           = true
  load_balancer_type = "network"
  subnets            = aws_subnet.private[*].id

  enable_deletion_protection = false

  tags = {
    Name = "${var.cluster_name}-nlb"
  }
}

resource "aws_lb_target_group" "etcd" {
  count = var.create_nlb ? 1 : 0

  name        = "${var.cluster_name}-tg"
  port        = 2379
  protocol    = "TCP"
  vpc_id      = aws_vpc.etcd_vpc.id
  target_type = "instance"

  health_check {
    protocol = "TCP"
    port     = 2379
    interval = 30
  }
}

resource "aws_lb_listener" "etcd" {
  count = var.create_nlb ? 1 : 0

  load_balancer_arn = aws_lb.etcd[0].arn
  port              = "2379"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.etcd[0].arn
  }
}

# ==============================================================================
# Route 53 Alias Record for the NLB
# ==============================================================================

resource "aws_route53_record" "etcd_alias" {
  count = var.create_nlb && var.route53_zone_id != "" && var.etcd_dns_name != "" ? 1 : 0

  zone_id = var.route53_zone_id
  name    = var.etcd_dns_name
  type    = "A"

  alias {
    name                   = aws_lb.etcd[0].dns_name
    zone_id                = aws_lb.etcd[0].zone_id
    evaluate_target_health = true
  }
}
