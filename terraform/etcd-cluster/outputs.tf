# ==============================================================================
# Outputs
# ==============================================================================

output "etcd_cluster_endpoint" {
  description = "The recommended DNS endpoint for your applications to connect to the etcd cluster."
  value = var.create_nlb ? (
    var.route53_zone_id != "" && var.etcd_dns_name != "" ?
    aws_route53_record.etcd_alias[0].fqdn :
    aws_lb.etcd[0].dns_name
  ) : "NLB not created. Connect to individual node IPs."
}
