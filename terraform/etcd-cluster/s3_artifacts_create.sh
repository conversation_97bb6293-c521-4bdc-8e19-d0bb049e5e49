#!/bin/bash

# ==============================================================================
# 该脚本用于自动化以下准备工作：
# 1. 创建一个用于存储工件的 S3 存储桶。
# 2. 从 GitHub 下载指定版本的 etcd。
# 3. 将下载的 etcd 安装包上传到 S3 存储桶中。
#
# 使用方法:
# 1. 保存为 setup_artifacts.sh
# 2. chmod +x setup_artifacts.sh
# 3. ./setup_artifacts.sh
#
# 前提: 已安装并配置好 AWS CLI。
# ==============================================================================

# --- 可配置变量 ---
# S3 存储桶名称 (S3 桶名是全局唯一的，如果这个名字已被占用，请修改它)
BUCKET_NAME="playwind-artifacts-bucket-us-east-1"
# AWS 区域 (应与您的 Terraform 配置保持一致)
AWS_REGION="us-east-1"
# 您希望下载和上传的 etcd 版本
ETCD_VER="v3.6.4" # 这是 v3.5 系列的一个稳定版本

# --- 脚本逻辑 ---

# 设置颜色用于输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

echo -e "${GREEN}脚本开始...${NC}"

# 1. 创建 S3 存储桶
echo -e "\n${YELLOW}步骤 1: 正在尝试创建 S3 存储桶 '${BUCKET_NAME}'...${NC}"

if aws s3api head-bucket --bucket "${BUCKET_NAME}" 2>/dev/null; then
    echo "存储桶 '${BUCKET_NAME}' 已存在，跳过创建步骤。"
else
    echo "存储桶不存在，正在创建..."
    # 修复: 针对 us-east-1 区域的特殊处理
    if [ "${AWS_REGION}" == "us-east-1" ]; then
        # us-east-1 区域不需要 LocationConstraint
        aws s3api create-bucket \
            --bucket "${BUCKET_NAME}" \
            --region "${AWS_REGION}"
    else
        # 其他区域需要 LocationConstraint
        aws s3api create-bucket \
            --bucket "${BUCKET_NAME}" \
            --region "${AWS_REGION}" \
            --create-bucket-configuration LocationConstraint="${AWS_REGION}"
    fi
    echo -e "${GREEN}成功创建 S3 存储桶 '${BUCKET_NAME}'.${NC}"
fi


# 2. 下载 etcd 安装包
ETCD_FILENAME="etcd-${ETCD_VER}-linux-amd64.tar.gz"
DOWNLOAD_URL="https://github.com/etcd-io/etcd/releases/download/${ETCD_VER}/${ETCD_FILENAME}"

echo -e "\n${YELLOW}步骤 2: 正在从 GitHub 下载 etcd ${ETCD_VER}...${NC}"
echo "下载链接: ${DOWNLOAD_URL}"

# 检查文件是否已存在，避免重复下载
if [ -f "/tmp/${ETCD_FILENAME}" ]; then
    echo "文件 '/tmp/${ETCD_FILENAME}' 已存在，跳过下载。"
else
    curl -L "${DOWNLOAD_URL}" -o "/tmp/${ETCD_FILENAME}"
    echo -e "${GREEN}成功下载 etcd 到 '/tmp/${ETCD_FILENAME}'.${NC}"
fi


# 3. 上传 etcd 安装包到 S3
echo -e "\n${YELLOW}步骤 3: 正在上传 '${ETCD_FILENAME}' 到 S3 存储桶 '${BUCKET_NAME}'...${NC}"
aws s3 cp "/tmp/${ETCD_FILENAME}" "s3://${BUCKET_NAME}/"

echo -e "\n${GREEN}所有步骤已成功完成！${NC}"
echo "您现在可以将 Terraform 变量 's3_bucket_for_artifacts' 的值设置为 '${BUCKET_NAME}' 并运行您的 Terraform 项目了。"

