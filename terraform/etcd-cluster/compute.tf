# ==============================================================================
# Compute Resources (Launch Template, Auto Scaling Groups)
# ==============================================================================

resource "aws_launch_template" "etcd_lt" {
  name_prefix   = "${var.cluster_name}-"
  image_id      = var.ami_id
  instance_type = var.instance_type

  block_device_mappings {
    device_name = "/dev/sdf"
    ebs {
      volume_size           = var.ebs_volume_size
      volume_type           = "gp3"
      delete_on_termination = true
    }
  }

  iam_instance_profile {
    arn = aws_iam_instance_profile.etcd_instance_profile.arn
  }

  vpc_security_group_ids = [aws_security_group.etcd_sg.id]

  # Render the external script file as user data
  user_data = base64encode(templatefile("${path.module}/scripts/configure-etcd.sh", {
    s3_bucket_for_artifacts = var.s3_bucket_for_artifacts
    aws_region              = var.aws_region
    cluster_name            = var.cluster_name
  }))

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name    = "${var.cluster_name}-node"
      Cluster = var.cluster_name
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Create one Auto Scaling Group per Availability Zone
resource "aws_autoscaling_group" "etcd_asg" {
  count = var.node_count

  name                = "${var.cluster_name}-asg-${count.index + 1}"
  vpc_zone_identifier = [aws_subnet.private[count.index].id]
  min_size            = 1
  max_size            = 1
  desired_capacity    = 1

  # Attach instances to the NLB target group
  target_group_arns = var.create_nlb ? [aws_lb_target_group.etcd[0].arn] : []

  launch_template {
    id      = aws_launch_template.etcd_lt.id
    version = "$Latest"
  }

  tag {
    key                 = "Cluster"
    value               = var.cluster_name
    propagate_at_launch = true
  }
}
