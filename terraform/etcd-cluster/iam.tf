# ==============================================================================
# IAM Roles and Policies
# ==============================================================================

resource "aws_iam_role" "etcd_node_role" {
  name = "${var.cluster_name}-node-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action    = "sts:AssumeRole"
        Effect    = "Allow"
        Principal = { Service = "ec2.amazonaws.com" }
      }
    ]
  })
}

resource "aws_iam_policy" "etcd_policy" {
  name        = "${var.cluster_name}-policy"
  description = "Policy for etcd nodes to discover peers and get artifacts from S3"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action   = ["ec2:DescribeInstances", "ec2:DescribeTags"]
        Effect   = "Allow"
        Resource = "*"
      },
      {
        Action   = ["s3:GetObject"]
        Effect   = "Allow"
        Resource = "arn:aws:s3:::${var.s3_bucket_for_artifacts}/*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "etcd_policy_attach" {
  role       = aws_iam_role.etcd_node_role.name
  policy_arn = aws_iam_policy.etcd_policy.arn
}

resource "aws_iam_instance_profile" "etcd_instance_profile" {
  name = "${var.cluster_name}-instance-profile"
  role = aws_iam_role.etcd_node_role.name
}
