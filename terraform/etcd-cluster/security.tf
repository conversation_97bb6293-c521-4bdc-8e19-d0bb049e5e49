# ==============================================================================
# Security Groups
# ==============================================================================

# Security group for the etcd nodes
resource "aws_security_group" "etcd_sg" {
  name        = "${var.cluster_name}-sg"
  description = "Security group for etcd nodes"
  vpc_id      = aws_vpc.etcd_vpc.id

  # Allow client traffic (port 2379) from within this VPC and the peered VPC
  ingress {
    description = "etcd client traffic from VPC and peered VPC"
    from_port   = 2379
    to_port     = 2379
    protocol    = "tcp"
    # Allow traffic from this VPC's CIDR and the peered VPC's CIDR
    cidr_blocks = compact(concat([aws_vpc.etcd_vpc.cidr_block], var.peer_vpc_cidr_block != "" ? [var.peer_vpc_cidr_block] : []))
  }

  # Allow peer traffic (port 2380) between nodes in this security group
  ingress {
    description = "etcd peer traffic"
    from_port   = 2380
    to_port     = 2380
    protocol    = "tcp"
    self        = true
  }

  # Allow SSH traffic (optional, for maintenance)
  # !!IMPORTANT!!: For production, restrict cidr_blocks to your bastion host's IP or security group.
  ingress {
    description = "SSH access"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # WARNING: Open to the world, please modify!
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${var.cluster_name}-sg"
  }
}

# Security group for the VPC Interface Endpoints
resource "aws_security_group" "vpc_endpoint_sg" {
  name        = "${var.cluster_name}-vpce-sg"
  description = "Security group for VPC Interface Endpoints"
  vpc_id      = aws_vpc.etcd_vpc.id

  # Allow HTTPS traffic from the etcd nodes for calling AWS APIs
  ingress {
    description     = "Allow HTTPS from etcd nodes"
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    security_groups = [aws_security_group.etcd_sg.id]
  }

  tags = {
    Name = "${var.cluster_name}-vpce-sg"
  }
}
