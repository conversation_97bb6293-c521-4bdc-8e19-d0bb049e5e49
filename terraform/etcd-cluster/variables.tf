# ==============================================================================
# Input Variables
# ==============================================================================

variable "aws_region" {
  description = "The AWS region to deploy the resources in."
  type        = string
  default     = "us-east-1"
}

variable "cluster_name" {
  description = "The name of the etcd cluster."
  type        = string
  default     = "prod-etcd-internal"
}

variable "instance_type" {
  description = "EC2 instance type for etcd nodes."
  type        = string
  default     = "t3.small"
}

variable "ami_id" {
  description = "AMI ID for the etcd nodes. This is for the latest Ubuntu LTS."
  type        = string
  # !!IMPORTANT!!: This is an Ubuntu 24.04 LTS AMI for us-east-1.
  # If you change the region, please find the corresponding Ubuntu AMI ID.
  default = "ami-034568121cfdea9c3"
}

variable "ebs_volume_size" {
  description = "Size of the EBS volume for the etcd data directory in GB."
  type        = number
  default     = 20
}

variable "node_count" {
  description = "Number of etcd nodes. Must be an odd number (e.g., 3 or 5)."
  type        = number
  default     = 3
}

variable "s3_bucket_for_artifacts" {
  description = "The name of the S3 bucket where you have pre-uploaded the etcd binary tarball."
  type        = string
  # !!IMPORTANT!!: Please replace this value with your own S3 bucket name.
  default = "'playwind-artifacts-bucket-us-east-1"
}
#  https://us-east-1.console.aws.amazon.com/vpcconsole/home?region=us-east-1#VpcDetails:VpcId=vpc-27bdfe5d
variable "peer_vpc_id" {
  description = "The VPC ID of the application's VPC to peer with (e.g., your default VPC)."
  type        = string
  default     = "vpc-27bdfe5d" # !!IMPORTANT!!: Fill in your application's VPC ID, e.g., vpc-xxxxxxxx
}

variable "peer_vpc_cidr_block" {
  description = "The CIDR block of the application's VPC to peer with."
  type        = string
  default     = "**********/16" # !!IMPORTANT!!: Fill in your application's VPC CIDR, e.g., **********/16
}

variable "create_nlb" {
  description = "Set to true to create an internal Network Load Balancer for the cluster."
  type        = bool
  default     = true
}

variable "route53_zone_id" {
  description = "The ID of the Route 53 private hosted zone to create the record in. If left empty, no record will be created."
  type        = string
  default     = "Z06363652DU4H3FACSAFC" # e.g., "Z0123456789ABCDEFGHIJ"
}

variable "route53_zone_name" {
  description = "The name of the Route 53 private hosted zone to create the record in. If left empty, no record will be created."
  type        = string
  default     = "pwglab.com"
}

variable "etcd_dns_name" {
  description = "The friendly DNS name to assign to the etcd cluster NLB (e.g., 'etcd.internal.your-company.com')."
  type        = string
  default     = "etcd.pwglab.com" # e.g., "etcd.internal.your-company.com"
}
