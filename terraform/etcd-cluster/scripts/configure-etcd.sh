#!/bin/bash -ex

# ==============================================================================
# This script is executed on instance boot via user_data.
# It installs and configures an etcd node.
# ==============================================================================

# Install necessary tools (for Ubuntu)
# Because there is no internet access, it's recommended to use a custom AMI
# with these tools pre-installed. This command will fail if they are not in the AMI.
apt-get update -y
apt-get install -y jq awscli

# Download etcd binaries from S3
# !!IMPORTANT!!: You must pre-upload the etcd tar.gz file to this S3 bucket.
ETCD_VER=v3.6.4
ETCD_TARBALL="etcd-$${ETCD_VER}-linux-amd64.tar.gz"
aws s3 cp s3://${s3_bucket_for_artifacts}/$${ETCD_TARBALL} /tmp/$${ETCD_TARBALL} --region ${aws_region}

tar xzvf /tmp/$${ETCD_TARBALL} -C /tmp --strip-components=1
mv /tmp/etcd /usr/local/bin/
mv /tmp/etcdctl /usr/local/bin/

# Create etcd user and data directory
groupadd --system etcd
useradd --system -g etcd -s /sbin/nologin -d /var/lib/etcd etcd
mkdir -p /var/lib/etcd

# Format and mount the dedicated EBS volume for etcd data
mkfs -t xfs /dev/sdf
mount /dev/sdf /var/lib/etcd
chown -R etcd:etcd /var/lib/etcd

# Configure the systemd service for etcd
cat > /etc/systemd/system/etcd.service << EOL
[Unit]
Description=etcd service
Documentation=https://github.com/etcd-io/etcd
After=network.target

[Service]
User=etcd
Type=notify
ExecStart=/usr/local/bin/etcd \\
  --name %NODE_NAME% \\
  --data-dir /var/lib/etcd \\
  --initial-advertise-peer-urls http://%PRIVATE_IP%:2380 \\
  --listen-peer-urls http://%PRIVATE_IP%:2380 \\
  --listen-client-urls http://%PRIVATE_IP%:2379,http://127.0.0.1:2379 \\
  --advertise-client-urls http://%PRIVATE_IP%:2379 \\
  --initial-cluster-token ${cluster_name}-token \\
  --initial-cluster-state new \\
  --initial-cluster %INITIAL_CLUSTER%
Restart=on-failure
RestartSec=5
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOL

# Dynamically discover cluster members and configure the service
REGION=$(curl -s http://***************/latest/dynamic/instance-identity/document | jq -r .region)
INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)
PRIVATE_IP=$(curl -s http://***************/latest/meta-data/local-ipv4)
NODE_NAME="${cluster_name}-node-$${INSTANCE_ID}"

# Replace node-specific placeholders in the service file
sed -i "s|%NODE_NAME%|$NODE_NAME|g" /etc/systemd/system/etcd.service
sed -i "s|%PRIVATE_IP%|$PRIVATE_IP|g" /etc/systemd/system/etcd.service

# Simple delay to allow other instances in the ASGs to start up
sleep 30

# Find all peer instances using the common 'Cluster' tag
PEER_IPS=$(aws ec2 describe-instances --region $REGION \
  --filters "Name=tag:Cluster,Values=${cluster_name}" "Name=instance-state-name,Values=running,pending" \
  --query "Reservations[*].Instances[*].[PrivateIpAddress, InstanceId]" \
  --output text)

INITIAL_CLUSTER_STRING=""
while read -r ip id; do
  if [ -n "$ip" ]; then
    INITIAL_CLUSTER_STRING="$${INITIAL_CLUSTER_STRING},${cluster_name}-node-$${id}=http://$${ip}:2380"
  fi
done <<< "$PEER_IPS"

# Exit with an error if no peer instances were found after waiting
if [ -z "$INITIAL_CLUSTER_STRING" ]; then
  echo "FATAL: Could not discover any peer instances. Exiting." >&2
  exit 1
fi

# Remove the leading comma using bash substring expansion.
# The double dollar sign "$$" is used to escape the interpolation for Terraform's templatefile function.
INITIAL_CLUSTER_STRING="$${INITIAL_CLUSTER_STRING:1}"

# Replace the cluster placeholder and start the service
sed -i "s|%INITIAL_CLUSTER%|$INITIAL_CLUSTER_STRING|g" /etc/systemd/system/etcd.service

systemctl daemon-reload
systemctl enable etcd
systemctl start etcd
