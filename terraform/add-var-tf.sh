#!/bin/bash
WORKSPACE_ID=$1
KEY=$2
VALUE=$3
# should not have blank character
DESCRIPTION=$4
TOKEN=$5
# true or false
SENSITIVE=${6:-true}

generate_post_data()
{
VAR_KEY=$1
VAR_VALUE=$2
VAR_DESCRIPTION=$3
VAR_SENSITIVE=$4
  cat <<EOF
{
  "data": {
    "type":"vars",
    "attributes": {
      "key":"${VAR_KEY}",
      "value":"${VAR_VALUE}",
      "description":"${VAR_DESCRIPTION}",
      "category":"terraform",
      "hcl":false,
      "sensitive":${VAR_SENSITIVE}
    },
    "relationships": {
      "workspace": {
        "data": {
          "id":"${WORKSPACE_ID}",
          "type":"workspaces"
        }
      }
    }
  }
}
EOF
}
# Add env key, value, description, sensitive
# https://developer.hashicorp.com/terraform/cloud-docs/api-docs/workspace-variables
curl \
  --header "Authorization: Bearer ${TOKEN}" \
  --header "Content-Type: application/vnd.api+json" \
  --request POST \
  --data "$(generate_post_data "${KEY}" "${VALUE}" "${DESCRIPTION}" "${SENSITIVE}")" \
  https://app.terraform.io/api/v2/vars
