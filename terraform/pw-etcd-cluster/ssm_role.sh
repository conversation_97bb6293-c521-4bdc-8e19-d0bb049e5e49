#!/bin/bash

# 替换为你的 EC2 实例 ID
INSTANCE_ID=i-042128cd4099a99b6

# 创建 IAM Role 名称和 Instance Profile 名称
ROLE_NAME=SSMAccessRole
INSTANCE_PROFILE_NAME=SSMAccessInstanceProfile

# 创建 IAM Role 信任策略（允许 EC2 使用）
cat > trust-policy.json <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

# 创建 IAM Role
aws iam create-role \
  --role-name $ROLE_NAME \
  --assume-role-policy-document file://trust-policy.json

# 附加 SSM 权限
aws iam attach-role-policy \
  --role-name $ROLE_NAME \
  --policy-arn arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore

# 创建 Instance Profile
aws iam create-instance-profile \
  --instance-profile-name $INSTANCE_PROFILE_NAME

# 添加 Role 到 Instance Profile
aws iam add-role-to-instance-profile \
  --instance-profile-name $INSTANCE_PROFILE_NAME \
  --role-name $ROLE_NAME

# 等待一下 Instance Profile 建立（AWS 有延迟）
echo "Waiting 15 seconds for instance profile to propagate..."
sleep 15

# 将 Instance Profile 附加到 EC2 实例
aws ec2 associate-iam-instance-profile \
  --instance-id $INSTANCE_ID \
  --iam-instance-profile Name=$INSTANCE_PROFILE_NAME