# # ==============================================================================
# # Provider Configuration and Data Sources
# # ==============================================================================
# terraform {
#   required_providers {
#     aws = {
#       source  = "hashicorp/aws"
#       version = "~> 5.0"
#     }
#   }
#   backend "remote" {
#     organization = "funkylab"
#
#     workspaces {
#       name = "pw-etcd-cluster"
#     }
#   }
# }
#
# # 添加默认的AWS提供者配置
# provider "aws" {
#   region = var.aws_region # 这个 region 会从 var.aws_region 获取，确保它被正确设置
# }
#
# provider "aws" {
#   alias  = "us_east_1"
#   region = "us-east-1"
# }
#
# data "aws_availability_zones" "available" {
#   state = "available"
# }
#
# locals {
#   cluster_name = var.cluster_name
#   region       = var.aws_region
#   domain_name  = var.route53_zone_name
#   app_name     = "etcd"
#   env          = "prod"
#   tags_vpc = {
#     Name   = local.app_name
#     Env    = local.env
#     App    = local.app_name
#     Module = "etcd_vpc"
#   }
#   tags_cluster = {
#     Name   = local.app_name
#     Env    = local.env
#     App    = local.app_name
#     Module = "etcd_cluster"
#   }
# }
#
# module "pw_etcd_vpc" {
#   source = "git::ssh://**************/isovalent/terraform-aws-vpc.git?ref=v1.13"
#   providers = {
#     aws = aws.us_east_1
#   }
#   cidr                          = "********/16"
#   name                          = local.cluster_name
#   region                        = local.region
#   tags                          = local.tags_vpc
#   bastion_host_enabled          = true
#   bastion_host_assign_public_ip = true
#   bastion_host_instance_type    = "t3.micro"
# }
#
# module "pw_etcd_cluster" {
#   source = "git::ssh://**************/isovalent/terraform-aws-etcd.git?ref=2.0"
#
#   providers = {
#     aws = aws.us_east_1
#   }
#   vpc_id       = module.pw_etcd_vpc.id
#   cluster_name = local.cluster_name
#   region       = local.region
#   tags         = local.tags_cluster
#   node_count   = var.node_count
#   domain_name  = local.domain_name
#   vpc_cidr     = module.pw_etcd_vpc.vpc_cidr_block
# }
#
# output "nodes" {
#   value       = module.pw_etcd_cluster.nodes.*
#   description = "ID, public and private IP address, and subnet ID of all nodes of the created cluster."
# }
#
# output "etcd-endpoint" {
#   value       = module.pw_etcd_cluster.etcd-endpoint
#   description = "etcd load balancer endpoint"
# }