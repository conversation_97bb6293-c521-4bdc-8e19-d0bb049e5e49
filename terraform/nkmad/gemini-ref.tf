# main.tf

# Terraform 版本和 AWS Provider 配置
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "remote" {
    organization = "funkylab"

    workspaces {
      name = "Nkmad_Prod_Env"
    }
  }
}

# variables.tf

variable "aws_region" {
  description = "AWS 区域"
  type        = string
  default     = "us-east-1"
}

variable "app_name" {
  description = "应用程序名称，用于资源命名"
  type        = string
  default     = "nakama-game"
}

variable "ami_owner" {
  description = "AMI 的拥有者 ID"
  type        = string
  default     = "099720109477" # 示例：Canonical (Ubuntu)
}

variable "ssh_key_name" {
  description = "用于 SSH 访问 EC2 实例的密钥对名称"
  type        = string
  # 请替换为您的密钥对名称
  default = "your-ssh-key-name"
}

variable "ssh_whitelist_cidrs" {
  description = "允许 SSH 访问的 IP CIDR 列表"
  type        = list(string)
  default     = ["0.0.0.0/0"] # 警告：生产环境请限制为您的开发/管理 IP
}

variable "onepanel_whitelist_cidrs" {
  description = "允许 1Panel 访问的 IP CIDR 列表"
  type        = list(string)
  default     = ["0.0.0.0/0"] # 警告：生产环境请限制为您的管理 IP
}

variable "cpu_credits_type" {
  description = "EC2 T 系列实例的 CPU 积分选项 (standard 或 unlimited)"
  type        = string
  default     = "unlimited"
}

variable "game_service_version" {
  description = "游戏服务的版本号 (如果您的 Docker Compose 中使用)"
  type        = string
  default     = "1.0.0"
}

variable "use_rds_db" {
  description = "是否使用 RDS 数据库 (true/false)"
  type        = bool
  default     = false # 默认为 false，如果使用外部数据库请设为 true
}

variable "nakama_db_host" {
  description = "Nakama 数据库主机名/IP"
  type        = string
  default     = "localhost" # 替换为您的数据库主机
}

variable "nakama_db_port" {
  description = "Nakama 数据库端口"
  type        = number
  default     = 5432 # PostgreSQL 默认端口
}

variable "nakama_db_name" {
  description = "Nakama 数据库名称"
  type        = string
  default     = "nakama"
}

variable "nakama_db_username" {
  description = "Nakama 数据库用户名"
  type        = string
  default     = "nakama"
}

variable "nakama_db_password" {
  description = "Nakama 数据库密码"
  type        = string
  sensitive   = true # 标记为敏感，避免直接输出到控制台
  default     = "your_nakama_db_password" # 请替换为强密码
}

variable "acm_certificate_arn" {
  description = "AWS Certificate Manager (ACM) 中 *.pwglab.com 通配符证书的 ARN"
  type        = string
  # 示例：arn:aws:acm:us-east-1:************:certificate/abcdefgh-1234-5678-9012-abcdefghijkl
  default = "arn:aws:acm:us-east-1:YOUR_AWS_ACCOUNT_ID:certificate/YOUR_CERTIFICATE_ID" # 请替换为您的证书 ARN
}

variable "nakama_realtime_instance_type" {
  description = "Nakama Realtime 服务器的 EC2 实例类型"
  type        = string
  default     = "t3.large" # 初始实例类型
}

variable "nakama_db_rpc_instance_type" {
  description = "Nakama DB RPC 服务器的 EC2 实例类型"
  type        = string
  default     = "t3.medium" # ASG 实例类型
}

variable "nakama_db_rpc_min_instances" {
  description = "Nakama DB RPC Auto Scaling Group 的最小实例数"
  type        = number
  default     = 1
}

variable "nakama_db_rpc_max_instances" {
  description = "Nakama DB RPC Auto Scaling Group 的最大实例数"
  type        = number
  default     = 5 # 可以根据需求调整
}

variable "nakama_db_rpc_desired_instances" {
  description = "Nakama DB RPC Auto Scaling Group 的期望实例数"
  type        = number
  default     = 1
}

provider "aws" {
  region = var.aws_region # 使用变量定义 AWS 区域
}

# 引用共享模块，假定其中包含 VPC、子网、Route 53 Zone 等共享资源
module "shared" {
  source   = "./shared"
  app_name = var.app_name
}

# 获取默认 VPC 信息
data "aws_vpc" "default" {
  default = true
}

# 获取所有可用区
data "aws_availability_zones" "available" {
  state = "available"
}

# 获取 Docker 服务器 AMI
data "aws_ami" "docker_server" {
  most_recent = true
  owners = [
    var.ami_owner
  ]
  filter {
    name = "name"
    values = [
      "ubuntu-docker-server-${var.app_name}-prod-latest"
    ]
  }
}

# ==============================================================================
# 安全组 (Security Groups)
# ==============================================================================

# NLB 安全组：允许所有必要的入站流量
resource "aws_security_group" "nakama_nlb_sg" {
  name        = "${var.app_name}-nakama-nlb-sg-prod"
  description = "Allow inbound traffic for Nakama NLB"
  vpc_id      = data.aws_vpc.default.id

  # 7350 (HTTP API)
  ingress {
    from_port   = 7350
    to_port     = 7350
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7450 (Realtime HTTP API)
  ingress {
    from_port   = 7450
    to_port     = 7450
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7351 (HTTP API - Embedded Developer Console)
  ingress {
    from_port   = 7351
    to_port     = 7351
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7451 (Realtime HTTP API - Special Console)
  ingress {
    from_port   = 7451
    to_port     = 7451
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7349 (gRPC API)
  ingress {
    from_port   = 7349
    to_port     = 7349
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7449 (Realtime gRPC API)
  ingress {
    from_port   = 7449
    to_port     = 7449
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 24914 (1Panel 服务端口) - 如果需要通过 NLB 暴露
  # ingress {
  #   from_port   = 24914
  #   to_port     = 24914
  #   protocol    = "tcp"
  #   cidr_blocks = ["0.0.0.0/0"]
  # }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name    = "${var.app_name}-nakama-nlb-sg-prod"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
  }
}

# Nakama 实例安全组：允许来自 NLB 和 SSH 的流量
resource "aws_security_group" "nakama_instance_sg" {
  name        = "${var.app_name}-nakama-instance-sg-prod"
  description = "Allow inbound traffic from NLB and SSH for Nakama instances"
  vpc_id      = data.aws_vpc.default.id

  # 允许来自 NLB 的所有端口流量
  ingress {
    from_port       = 0
    to_port         = 0
    protocol        = "-1"
    security_groups = [aws_security_group.nakama_nlb_sg.id]
    description     = "Allow all traffic from NLB"
  }

  # 允许 SSH 访问
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.ssh_whitelist_cidrs # 从变量获取 SSH 白名单
    description = "Allow SSH access"
  }

  # 允许 1Panel 端口访问 (如果 1Panel 直接暴露)
  ingress {
    from_port   = 24914
    to_port     = 24914
    protocol    = "tcp"
    cidr_blocks = var.onepanel_whitelist_cidrs # 从变量获取 1Panel 白名单
    description = "Allow 1Panel access"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name    = "${var.app_name}-nakama-instance-sg-prod"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
  }
}

# ==============================================================================
# IAM 角色和策略 (IAM Roles and Policies)
# ==============================================================================

# EC2 实例的 IAM 角色，用于允许实例执行操作（例如上传 CloudWatch 指标）
resource "aws_iam_role" "app_prod_ec2_role" {
  name = "${var.app_name}-prod-ec2-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "",
        Effect = "Allow",
        Principal = {
          Service = [
            "ec2.amazonaws.com"
          ]
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# EC2 实例配置文件
resource "aws_iam_instance_profile" "app_prod_ec2_profile" {
  name = "${var.app_name}-prod-ec2-profile"
  role = aws_iam_role.app_prod_ec2_role.name
}

# CloudWatch Agent 自定义策略
resource "aws_iam_policy" "cloudwatch_agent_custom_policy" {
  name        = "${var.app_name}-CloudWatchAgentCustomPolicy"
  description = "Custom policy for CloudWatch Agent to send metrics and logs"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "cloudwatch:PutMetricData",
          "ec2:DescribeTags",
          "ec2:DescribeVolumes",
          "ec2:DescribeInstances"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:PutLogEvents",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutRetentionPolicy"
        ]
        Resource = "arn:aws:logs:${var.aws_region}:*:log-group:/aws/ec2/cloudwatch-agent/*"
      }
    ]
  })
}

# 将 CloudWatch Agent 策略附加到 EC2 角色
resource "aws_iam_role_policy_attachment" "cloudwatch_agent_custom_policy_attach" {
  role       = aws_iam_role.app_prod_ec2_role.name
  policy_arn = aws_iam_policy.cloudwatch_agent_custom_policy.arn
}

# ==============================================================================
# Nakama Realtime 服务器 (单节点，纵向扩容)
# ==============================================================================

resource "aws_instance" "nakama_realtime_server" {
  ami           = data.aws_ami.docker_server.id
  instance_type = var.nakama_realtime_instance_type # 从变量获取实例类型

  vpc_security_group_ids = [
    aws_security_group.nakama_instance_sg.id,
  ]

  # user_data 脚本用于安装 Docker、Docker Compose、CloudWatch Agent 并启动 Nakama
  user_data = templatefile("${path.module}/user-data-game.sh", {
    nakama_role          = "realtime" # 传递角色标识
    game_service_version = var.game_service_version
    use_rds_db           = var.use_rds_db
    nakama_db_host       = var.nakama_db_host
    nakama_db_port       = var.nakama_db_port
    nakama_db_name       = var.nakama_db_name
    nakama_db_username   = var.nakama_db_username
    nakama_db_password   = var.nakama_db_password
    aws_region           = var.aws_region
  })
  key_name             = var.ssh_key_name
  iam_instance_profile = aws_iam_instance_profile.app_prod_ec2_profile.name
  credit_specification {
    cpu_credits = var.cpu_credits_type
  }

  tags = {
    Name    = "${var.app_name}-nakama-realtime-prod"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
    Role    = "Realtime"
  }
}

# 为 Realtime 服务器分配弹性 IP
resource "aws_eip" "nakama_realtime_eip" {
  domain   = "vpc"
  instance = aws_instance.nakama_realtime_server.id
  tags = {
    Name = "${var.app_name}-nakama-realtime-eip-prod"
  }
}

# 为 Realtime 服务器创建 Route 53 A 记录
resource "aws_route53_record" "nakama_realtime_record" {
  zone_id = module.shared.zone_id
  name    = "realtime.${var.app_name}.${module.shared.zone_name}"
  type    = "A"
  ttl     = "300"
  records = [
    aws_eip.nakama_realtime_eip.public_ip
  ]
  allow_overwrite = true
}


# ==============================================================================
# Nakama DB RPC 服务器 (横向扩容 - Auto Scaling Group)
# ==============================================================================

# Launch Template for Nakama DB RPC instances
resource "aws_launch_template" "nakama_db_rpc_lt" {
  name_prefix   = "${var.app_name}-nakama-db-rpc-lt-"
  image_id      = data.aws_ami.docker_server.id
  instance_type = var.nakama_db_rpc_instance_type # 从变量获取实例类型
  key_name      = var.ssh_key_name
  vpc_security_group_ids = [
    aws_security_group.nakama_instance_sg.id,
  ]
  iam_instance_profile {
    name = aws_iam_instance_profile.app_prod_ec2_profile.name
  }
  user_data = base64encode(templatefile("${path.module}/user-data-game.sh", {
    nakama_role          = "db_rpc" # 传递角色标识
    game_service_version = var.game_service_version
    use_rds_db           = var.use_rds_db
    nakama_db_host       = var.nakama_db_host
    nakama_db_port       = var.nakama_db_port
    nakama_db_name       = var.nakama_db_name
    nakama_db_username   = var.nakama_db_username
    nakama_db_password   = var.nakama_db_password
    aws_region           = var.aws_region
  }))
  credit_specification {
    cpu_credits = var.cpu_credits_type
  }

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name    = "${var.app_name}-nakama-db-rpc-prod"
      Service = "game"
      App     = var.app_name
      Env     = "prod"
      Role    = "DB_RPC"
    }
  }
}

# Auto Scaling Group for Nakama DB RPC
resource "aws_autoscaling_group" "nakama_db_rpc_asg" {
  name                      = "${var.app_name}-nakama-db-rpc-asg-prod"
  max_size                  = var.nakama_db_rpc_max_instances
  min_size                  = var.nakama_db_rpc_min_instances
  desired_capacity          = var.nakama_db_rpc_desired_instances
  vpc_zone_identifier       = module.shared.private_subnet_ids # 使用共享模块提供的私有子网
  target_group_arns         = [
    aws_lb_target_group.nakama_7350_tg.arn, # DB RPC 主要处理 7350 端口流量
    aws_lb_target_group.nakama_7351_tg.arn, # DB RPC 上的 7351 端口
    aws_lb_target_group.nakama_7349_tg.arn, # DB RPC 上的 7349 端口
    aws_lb_target_group.nakama_9100_tg.arn, # Prometheus 端口
    aws_lb_target_group.nakama_24914_tg.arn # 1Panel 端口
  ]
  health_check_type         = "ELB"
  health_check_grace_period = 300 # 5 minutes
  force_delete              = true # 强制删除 ASG，即使有实例在运行

  launch_template {
    id      = aws_launch_template.nakama_db_rpc_lt.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "${var.app_name}-nakama-db-rpc-prod"
    propagate_at_launch = true
  }
  tag {
    key                 = "Service"
    value               = "game"
    propagate_at_launch = true
  }
  tag {
    key                 = "App"
    value               = var.app_name
    propagate_at_launch = true
  }
  tag {
    key                 = "Env"
    value               = "prod"
    propagate_at_launch = true
  }
  tag {
    key                 = "Role"
    value               = "DB_RPC"
    propagate_at_launch = true
  }
}

# 扩容策略：CPU 使用率
resource "aws_autoscaling_policy" "cpu_scaling_up" {
  name                   = "${var.app_name}-cpu-scaling-up"
  scaling_adjustment     = 1 # 增加 1 个实例
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "cpu_high" {
  alarm_name          = "${var.app_name}-cpu-high-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 60
  statistic           = "Average"
  threshold           = 90 # CPU 使用率达到 90%
  alarm_description   = "This alarm monitors EC2 CPU utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.cpu_scaling_up.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 扩容策略：内存使用率 (需要 CloudWatch Agent 上报)
resource "aws_autoscaling_policy" "memory_scaling_up" {
  name                   = "${var.app_name}-memory-scaling-up"
  scaling_adjustment     = 1
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "memory_high" {
  alarm_name          = "${var.app_name}-memory-high-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "mem_used_percent" # CloudWatch Agent 上报的内存指标
  namespace           = "CWAgent"          # CloudWatch Agent 的命名空间
  period              = 60
  statistic           = "Average"
  threshold           = 90 # 内存使用率达到 90%
  alarm_description   = "This alarm monitors EC2 memory utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.memory_scaling_up.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 扩容策略：磁盘使用率 (需要 CloudWatch Agent 上报)
resource "aws_autoscaling_policy" "disk_scaling_up" {
  name                   = "${var.app_name}-disk-scaling-up"
  scaling_adjustment     = 1
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "disk_high" {
  alarm_name          = "${var.app_name}-disk-high-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "disk_used_percent" # CloudWatch Agent 上报的磁盘指标
  namespace           = "CWAgent"           # CloudWatch Agent 的命名空间
  period              = 60
  statistic           = "Average"
  threshold           = 70 # 磁盘使用率达到 70%
  alarm_description   = "This alarm monitors EC2 disk utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.disk_scaling_up.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 缩容策略：CPU 使用率
resource "aws_autoscaling_policy" "cpu_scaling_down" {
  name                   = "${var.app_name}-cpu-scaling-down"
  scaling_adjustment     = -1 # 减少 1 个实例
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "cpu_low" {
  alarm_name          = "${var.app_name}-cpu-low-alarm"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 60
  statistic           = "Average"
  threshold           = 30 # CPU 使用率低于 30%
  alarm_description   = "This alarm monitors EC2 CPU utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.cpu_scaling_down.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 缩容策略：内存使用率
resource "aws_autoscaling_policy" "memory_scaling_down" {
  name                   = "${var.app_name}-memory-scaling-down"
  scaling_adjustment     = -1
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "memory_low" {
  alarm_name          = "${var.app_name}-memory-low-alarm"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "mem_used_percent"
  namespace           = "CWAgent"
  period              = 60
  statistic           = "Average"
  threshold           = 30 # 内存使用率低于 30%
  alarm_description   = "This alarm monitors EC2 memory utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.memory_scaling_down.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}


# ==============================================================================
# 网络负载均衡器 (Network Load Balancer - NLB)
# ==============================================================================

resource "aws_lb" "nakama_nlb" {
  name               = "${var.app_name}-nakama-nlb-prod"
  internal           = false
  load_balancer_type = "network"
  subnets            = module.shared.public_subnet_ids # 使用共享模块提供的公共子网
  security_groups    = [aws_security_group.nakama_nlb_sg.id] # 附加 NLB 安全组

  enable_cross_zone_load_balancing = true # 启用跨可用区负载均衡
  tags = {
    Name    = "${var.app_name}-nakama-nlb-prod"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
  }
}

# ==============================================================================
# 目标组 (Target Groups)
# ==============================================================================

# 目标组：7350 -> 两套服务器的 7350 端口 (用于 DB RPC)
resource "aws_lb_target_group" "nakama_7350_tg" {
  name        = "${var.app_name}-nakama-7350-tg"
  port        = 7350
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance" # 目标类型为实例

  health_check {
    protocol            = "TCP"
    port                = 7350
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 5
    interval            = 10
  }
  tags = {
    Name = "${var.app_name}-nakama-7350-tg"
  }
}

# 目标组：7450 -> Nakama Realtime 服务器的 7350 端口 (用于 Realtime 功能)
resource "aws_lb_target_group" "nakama_7450_tg" {
  name        = "${var.app_name}-nakama-7450-tg"
  port        = 7350 # 实例上的目标端口
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  health_check {
    protocol            = "TCP"
    port                = 7350
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 5
    interval            = 10
  }
  tags = {
    Name = "${var.app_name}-nakama-7450-tg"
  }
}

# 目标组：7351 -> Nakama DB RPC 服务器的 7351 端口 (用于 Nakama Console)
resource "aws_lb_target_group" "nakama_7351_tg" {
  name        = "${var.app_name}-nakama-7351-tg"
  port        = 7351
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  health_check {
    protocol            = "TCP"
    port                = 7351
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 5
    interval            = 10
  }
  tags = {
    Name = "${var.app_name}-nakama-7351-tg"
  }
}

# 目标组：7451 -> Nakama Realtime 服务器的 7351 端口 (用于特殊用途)
resource "aws_lb_target_group" "nakama_7451_tg" {
  name        = "${var.app_name}-nakama-7451-tg"
  port        = 7351 # 实例上的目标端口
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  health_check {
    protocol            = "TCP"
    port                = 7351
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 5
    interval            = 10
  }
  tags = {
    Name = "${var.app_name}-nakama-7451-tg"
  }
}

# 目标组：7349 -> 两套服务器的 7349 端口
resource "aws_lb_target_group" "nakama_7349_tg" {
  name        = "${var.app_name}-nakama-7349-tg"
  port        = 7349
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  health_check {
    protocol            = "TCP"
    port                = 7349
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 5
    interval            = 10
  }
  tags = {
    Name = "${var.app_name}-nakama-7349-tg"
  }
}

# 目标组：7449 -> Nakama Realtime 服务器的 7349 端口
resource "aws_lb_target_group" "nakama_7449_tg" {
  name        = "${var.app_name}-nakama-7449-tg"
  port        = 7349 # 实例上的目标端口
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  health_check {
    protocol            = "TCP"
    port                = 7349
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 5
    interval            = 10
  }
  tags = {
    Name = "${var.app_name}-nakama-7449-tg"
  }
}

# 目标组：9100 -> Prometheus Metrics
resource "aws_lb_target_group" "nakama_9100_tg" {
  name        = "${var.app_name}-nakama-9100-tg"
  port        = 9100
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  health_check {
    protocol            = "TCP"
    port                = 9100
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 5
    interval            = 10
  }
  tags = {
    Name = "${var.app_name}-nakama-9100-tg"
  }
}

# 目标组：24914 -> 1Panel 服务端口
resource "aws_lb_target_group" "nakama_24914_tg" {
  name        = "${var.app_name}-nakama-24914-tg"
  port        = 24914
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  health_check {
    protocol            = "TCP"
    port                = 24914
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 5
    interval            = 10
  }
  tags = {
    Name = "${var.app_name}-nakama-24914-tg"
  }
}

# ==============================================================================
# NLB 监听器 (NLB Listeners)
# ==============================================================================

# 监听器：7350 -> 两套服务器的 7350 端口 (用于 DB RPC)
resource "aws_lb_listener" "nakama_7350_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7350
  protocol          = "TCP"
  ssl_policy        = "ELBSecurityPolicy-2016-08" # 推荐使用最新的安全策略
  certificate_arn   = var.acm_certificate_arn # ACM 证书 ARN

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7350_tg.arn
  }
}

# 监听器：7450 -> Nakama Realtime 服务器的 7350 端口 (用于 Realtime 功能)
resource "aws_lb_listener" "nakama_7450_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7450
  protocol          = "TCP"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7450_tg.arn
  }
}

# 监听器：7351 -> Nakama DB RPC 服务器的 7351 端口 (用于 Nakama Console)
resource "aws_lb_listener" "nakama_7351_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7351
  protocol          = "TCP"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7351_tg.arn
  }
}

# 监听器：7451 -> Nakama Realtime 服务器的 7351 端口 (用于特殊用途)
resource "aws_lb_listener" "nakama_7451_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7451
  protocol          = "TCP"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7451_tg.arn
  }
}

# 监听器：7349 -> 两套服务器的 7349 端口
resource "aws_lb_listener" "nakama_7349_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7349
  protocol          = "TCP"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7349_tg.arn
  }
}

# 监听器：7449 -> Nakama Realtime 服务器的 7349 端口
resource "aws_lb_listener" "nakama_7449_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7449
  protocol          = "TCP"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7449_tg.arn
  }
}

# 监听器：9100 -> Prometheus Metrics (如果需要通过 NLB 暴露)
resource "aws_lb_listener" "nakama_9100_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 9100
  protocol          = "TCP"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_9100_tg.arn
  }
}

# 监听器：24914 -> 1Panel 服务端口 (如果需要通过 NLB 暴露)
resource "aws_lb_listener" "nakama_24914_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 24914
  protocol          = "TCP"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_24914_tg.arn
  }
}

# ==============================================================================
# 目标组注册 (Target Group Attachments)
# ==============================================================================

# 将 Nakama Realtime 服务器注册到相应的目标组
resource "aws_lb_target_group_attachment" "realtime_7450_attachment" {
  target_group_arn = aws_lb_target_group.nakama_7450_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 7350
}

resource "aws_lb_target_group_attachment" "realtime_7451_attachment" {
  target_group_arn = aws_lb_target_group.nakama_7451_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 7351
}

resource "aws_lb_target_group_attachment" "realtime_7449_attachment" {
  target_group_arn = aws_lb_target_group.nakama_7449_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 7349
}

# 将 Nakama Realtime 服务器也注册到共享的 7350/7349/9100/24914 目标组
resource "aws_lb_target_group_attachment" "realtime_7350_attachment" {
  target_group_arn = aws_lb_target_group.nakama_7350_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 7350
}

resource "aws_lb_target_group_attachment" "realtime_7349_attachment" {
  target_group_arn = aws_lb_target_group.nakama_7349_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 7349
}

resource "aws_lb_target_group_attachment" "realtime_9100_attachment" {
  target_group_arn = aws_lb_target_group.nakama_9100_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 9100
}

resource "aws_lb_target_group_attachment" "realtime_24914_attachment" {
  target_group_arn = aws_lb_target_group.nakama_24914_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 24914
}


# ==============================================================================
# Route 53 DNS 记录 (Route 53 DNS Records)
# ==============================================================================

# 为 NLB 创建 Route 53 A 记录 (主入口)
resource "aws_route53_record" "nakama_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "nakama.${var.app_name}.${module.shared.zone_name}" # 例如: nakama.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 Realtime 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_realtime_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "realtime-api.${var.app_name}.${module.shared.zone_name}" # 例如: realtime-api.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 Console 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_console_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "console.${var.app_name}.${module.shared.zone_name}" # 例如: console.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 Realtime Console 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_realtime_console_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "realtime-console.${var.app_name}.${module.shared.zone_name}" # 例如: realtime-console.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 gRPC 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_grpc_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "grpc.${var.app_name}.${module.shared.zone_name}" # 例如: grpc.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 Realtime gRPC 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_realtime_grpc_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "realtime-grpc.${var.app_name}.${module.shared.zone_name}" # 例如: realtime-grpc.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 Prometheus 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_prometheus_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "prometheus.${var.app_name}.${module.shared.zone_name}" # 例如: prometheus.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 1Panel 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_onepanel_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "onepanel.${var.app_name}.${module.shared.zone_name}" # 例如: onepanel.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}


# outputs.tf

output "nakama_realtime_public_ip" {
  description = "Nakama Realtime 服务器的弹性 IP 地址"
  value       = aws_eip.nakama_realtime_eip.public_ip
}

output "nakama_realtime_dns_name" {
  description = "Nakama Realtime 服务器的 DNS 名称"
  value       = aws_route53_record.nakama_realtime_record.fqdn
}

output "nakama_nlb_dns_name" {
  description = "Nakama NLB 的 DNS 名称"
  value       = aws_lb.nakama_nlb.dns_name
}

output "nakama_api_url" {
  description = "Nakama HTTP API (DB RPC) 的 URL"
  value       = "https://${aws_route53_record.nakama_nlb_record.fqdn}:7350"
}

output "nakama_realtime_api_url" {
  description = "Nakama Realtime HTTP API 的 URL"
  value       = "https://${aws_route53_record.nakama_realtime_nlb_record.fqdn}:7450"
}

output "nakama_db_rpc_console_url" {
  description = "Nakama DB RPC Console 的 URL"
  value       = "https://${aws_route53_record.nakama_console_nlb_record.fqdn}:7351"
}

output "nakama_realtime_console_url" {
  description = "Nakama Realtime Console 的 URL"
  value       = "https://${aws_route53_record.nakama_realtime_console_nlb_record.fqdn}:7451"
}

output "nakama_grpc_url" {
  description = "Nakama gRPC API 的 URL"
  value       = "https://${aws_route53_record.nakama_grpc_nlb_record.fqdn}:7349"
}

output "nakama_realtime_grpc_url" {
  description = "Nakama Realtime gRPC API 的 URL"
  value       = "https://${aws_route53_record.nakama_realtime_grpc_nlb_record.fqdn}:7449"
}

output "nakama_prometheus_url" {
  description = "Nakama Prometheus Metrics URL"
  value       = "https://${aws_route53_record.nakama_prometheus_nlb_record.fqdn}:9100"
}

output "nakama_onepanel_url" {
  description = "Nakama 1Panel URL"
  value       = "https://${aws_route53_record.nakama_onepanel_nlb_record.fqdn}:24914"
}

