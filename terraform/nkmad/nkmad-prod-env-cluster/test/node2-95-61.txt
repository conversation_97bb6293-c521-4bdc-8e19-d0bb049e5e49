+ /nakama/nakama migrate up --database.address ltGqqg1Hka:<EMAIL>:26257/defaultdb?sslmode=verify-full
{"level":"info","ts":"2025-08-07T09:44:39.419Z","caller":"server/db.go:140","msg":"Database information","version":"CockroachDB CCL v25.2.1 (x86_64-pc-linux-gnu, built 2025/06/02 23:17:23, go1.23.7 X:nocoverageredesign)"}
{"level":"info","ts":"2025-08-07T09:44:39.419Z","caller":"migrate/migrate.go:109","msg":"Applying database migrations","limit":-1}
{"level":"info","ts":"2025-08-07T09:44:39.432Z","caller":"migrate/migrate.go:116","msg":"Successfully applied migration","count":0}
+ exec /nakama/nakama --config /nakama/data/local.yml --name ad-95-61-mb1X --database.address ltGqqg1Hka:<EMAIL>:26257/defaultdb?sslmode=verify-full --logger.level INFO --metrics.prometheus_port 9101
{"level":"info","ts":"2025-08-07T09:44:39.453Z","caller":"server/config.go:96","msg":"Successfully loaded config file","path":"/nakama/data/local.yml"}
{"level":"warn","ts":"2025-08-07T09:44:39.454Z","caller":"server/config.go:343","msg":"WARNING: insecure default parameter value, change this for production!","param":"console.signing_key"}
{"level":"warn","ts":"2025-08-07T09:44:39.457Z","caller":"server/config.go:351","msg":"WARNING: insecure default parameter value, change this for production!","param":"session.encryption_key"}
{"level":"warn","ts":"2025-08-07T09:44:39.457Z","caller":"server/config.go:355","msg":"WARNING: insecure default parameter value, change this for production!","param":"session.refresh_encryption_key"}
{"level":"info","ts":"2025-08-07T09:44:39.457Z","caller":"main.go:139","msg":"Nakama starting"}
{"level":"info","ts":"2025-08-07T09:44:39.457Z","caller":"main.go:140","msg":"Node","name":"ad-95-61-mb1X","version":"3.28.2+f54deba","runtime":"go1.24.5","cpu":2,"proc":2}
{"level":"info","ts":"2025-08-07T09:44:39.457Z","caller":"main.go:141","msg":"Data directory","path":"./data/"}
{"level":"info","ts":"2025-08-07T09:44:39.457Z","caller":"main.go:152","msg":"Database connections","dsns":["ltGqqg1Hka:<EMAIL>:26257/defaultdb?sslmode=verify-full"]}
{"level":"info","ts":"2025-08-07T09:44:39.518Z","caller":"server/db.go:140","msg":"Database information","version":"CockroachDB CCL v25.2.1 (x86_64-pc-linux-gnu, built 2025/06/02 23:17:23, go1.23.7 X:nocoverageredesign)"}
{"level":"info","ts":"2025-08-07T09:44:39.526Z","caller":"server/metrics.go:178","msg":"Starting Prometheus server for metrics requests","port":9101}
{"level":"info","ts":"2025-08-07T09:44:39.563Z","caller":"server/leaderboard_rank_cache.go:140","msg":"Initializing leaderboard rank cache"}
{"level":"info","ts":"2025-08-07T09:44:39.565Z","caller":"server/runtime.go:672","msg":"Initialising runtime","path":"data/modules"}
{"level":"info","ts":"2025-08-07T09:44:39.565Z","caller":"server/runtime.go:679","msg":"Initialising runtime event queue processor"}
{"level":"info","ts":"2025-08-07T09:44:39.567Z","caller":"server/runtime.go:681","msg":"Runtime event queue processor started","size":65536,"workers":8}
{"level":"info","ts":"2025-08-07T09:44:39.568Z","caller":"server/runtime_go.go:2969","msg":"Initialising Go runtime provider","path":"data/modules"}
{"level":"info","ts":"2025-08-07T09:44:39.568Z","caller":"server/runtime_go.go:2993","msg":"Go runtime modules loaded"}
{"level":"info","ts":"2025-08-07T09:44:39.568Z","caller":"server/runtime_lua.go:119","msg":"Initialising Lua runtime provider","path":"data/modules"}
{"level":"info","ts":"2025-08-07T09:44:39.569Z","caller":"server/runtime_lua.go:1278","msg":"Lua runtime modules loaded"}
{"level":"info","ts":"2025-08-07T09:44:39.569Z","caller":"server/runtime_lua.go:1281","msg":"Allocating minimum Lua runtime pool","count":16}
{"level":"info","ts":"2025-08-07T09:44:39.569Z","caller":"server/runtime_lua.go:1289","msg":"Allocated minimum Lua runtime pool"}
{"level":"info","ts":"2025-08-07T09:44:39.569Z","caller":"server/runtime_javascript.go:652","msg":"Initialising JavaScript runtime provider","path":"data/modules","entrypoint":"build/index.js"}
{"level":"info","ts":"2025-08-07T09:44:39.704Z","caller":"server/leaderboard_rank_cache.go:201","msg":"Leaderboard rank cache initialization completed successfully","cached":["GlobalAttackPower","GlobalAttackPower_n0","GlobalPvpTrophy","GlobalPvpTrophy_n0","GlobalThemeDefense","GlobalThemeDefense_n0","GlobalThemeDefense_n0_r1_b1","AttackPowerTournament_n0_r1_b0","AttackPowerTournament_n0_r1_b1","AttackPowerTournament_n0_r1_b2","AttackPowerTournament_n0_r1_b3","AttackPowerTournament_n0_r1_b4","AttackPowerTournament_n0_r1_b5","PvpTrophyTournament_n0_r1_b0","PvpTrophyTournament_n0_r1_b1","PvpTrophyTournament_n0_r1_b2","PvpTrophyTournament_n0_r1_b3","PvpTrophyTournament_n0_r1_b4","PvpTrophyTournament_n0_r1_b5","GateBesiegeRank_n0_r105_b95001","SeasonPvpRank_n0_r105_b1","SeasonBattleRank_n0_r105_b1","SeasonScoreRank_n0_r105_b1","GlobalMonsterTide_n0_r105_b96001","GateBesiegeRank_n0_r203_b95001","SeasonBattleRank_n0_r203_b1","SeasonScoreRank_n0_r203_b1","SeasonPvpRank_n0_r203_b1","pp_global","AttackPowerTournament_n0_r2_b0","AttackPowerTournament_n0_r2_b3","PvpTrophyTournament_n0_r2_b0","PvpTrophyTournament_n0_r2_b3"],"skipped":[],"duration":"141.279092ms"}
{"level":"info","ts":"2025-08-07T09:44:39.875Z","caller":"server/runtime_javascript_logger.go:74","msg":"leaderboard \"pp_global\" created"}
{"level":"info","ts":"2025-08-07T09:44:39.882Z","caller":"server/runtime_javascript_logger.go:74","msg":"[===== SystemInit ===== (uid: )] configVersion existing in DB, init config from db directly"}
{"level":"info","ts":"2025-08-07T09:44:39.949Z","caller":"server/runtime_javascript_logger.go:74","msg":"[===== SystemInit ===== (uid: )] configVersion is: 12"}
{"level":"info","ts":"2025-08-07T09:44:39.952Z","caller":"server/runtime_javascript_logger.go:74","msg":"[===== SystemInit ===== (uid: )] [initialLeaderboard] leaderboard GlobalMonsterTide disabled, skip"}
{"level":"info","ts":"2025-08-07T09:44:39.953Z","caller":"server/runtime_javascript_logger.go:74","msg":"[===== SystemInit ===== (uid: )] [initialLeaderboard] leaderboard SeasonKnightWarRank disabled, skip"}
{"level":"info","ts":"2025-08-07T09:44:39.953Z","caller":"server/runtime_javascript_logger.go:74","msg":"[===== SystemInit ===== (uid: )] [initialLeaderboard] leaderboard SeasonBattleRank disabled, skip"}
{"level":"info","ts":"2025-08-07T09:44:39.953Z","caller":"server/runtime_javascript_logger.go:74","msg":"[===== SystemInit ===== (uid: )] [initialLeaderboard] leaderboard SeasonPvpRank disabled, skip"}
{"level":"info","ts":"2025-08-07T09:44:39.953Z","caller":"server/runtime_javascript_logger.go:74","msg":"[===== SystemInit ===== (uid: )] [initialLeaderboard] leaderboard SeasonScoreRank disabled, skip"}
{"level":"info","ts":"2025-08-07T09:44:39.953Z","caller":"server/runtime_javascript_logger.go:74","msg":"[===== SystemInit ===== (uid: )] [initialLeaderboard] leaderboard GateBesiegeRank disabled, skip"}
{"level":"info","ts":"2025-08-07T09:44:39.953Z","caller":"server/runtime_javascript_logger.go:74","msg":"[===== SystemInit ===== (uid: )] [CustomTournament] Has Tournament Config 2"}
{"level":"error","ts":"2025-08-07T09:44:39.953Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] Has Season Config 12"}
{"level":"error","ts":"2025-08-07T09:44:39.954Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeMon Jul 21 2025 00:00:00 GMT+0000 (UTC), EndTimeThu Aug 21 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.960Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeFri Aug 01 2025 00:00:00 GMT+0000 (UTC), EndTimeMon Sep 01 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.963Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeMon Aug 11 2025 00:00:00 GMT+0000 (UTC), EndTimeThu Sep 11 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.963Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeMon Aug 11 2025 00:00:00 GMT+0000 (UTC), EndTimeThu Sep 11 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.963Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeMon Jul 21 2025 00:00:00 GMT+0000 (UTC), EndTimeThu Aug 21 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.966Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeTue Jul 01 2025 00:00:00 GMT+0000 (UTC), EndTimeFri Aug 01 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.966Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeFri Jul 11 2025 00:00:00 GMT+0000 (UTC), EndTimeMon Aug 11 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.970Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeThu Aug 21 2025 00:00:00 GMT+0000 (UTC), EndTimeSun Sep 21 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.970Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeFri Aug 01 2025 00:00:00 GMT+0000 (UTC), EndTimeMon Sep 01 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.973Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeThu Aug 21 2025 00:00:00 GMT+0000 (UTC), EndTimeSun Sep 21 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.973Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeTue Jul 01 2025 00:00:00 GMT+0000 (UTC), EndTimeFri Aug 01 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.973Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeFri Jul 11 2025 00:00:00 GMT+0000 (UTC), EndTimeMon Aug 11 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.977Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] Has Season Config 12"}
{"level":"error","ts":"2025-08-07T09:44:39.977Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeMon Aug 11 2025 00:00:00 GMT+0000 (UTC), EndTimeThu Sep 11 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.977Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeMon Jul 21 2025 00:00:00 GMT+0000 (UTC), EndTimeThu Aug 21 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.980Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeTue Jul 01 2025 00:00:00 GMT+0000 (UTC), EndTimeFri Aug 01 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.980Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeFri Jul 11 2025 00:00:00 GMT+0000 (UTC), EndTimeMon Aug 11 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.985Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeThu Aug 21 2025 00:00:00 GMT+0000 (UTC), EndTimeSun Sep 21 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.986Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeFri Aug 01 2025 00:00:00 GMT+0000 (UTC), EndTimeMon Sep 01 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.989Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeThu Aug 21 2025 00:00:00 GMT+0000 (UTC), EndTimeSun Sep 21 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.989Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeTue Jul 01 2025 00:00:00 GMT+0000 (UTC), EndTimeFri Aug 01 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.989Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeFri Jul 11 2025 00:00:00 GMT+0000 (UTC), EndTimeMon Aug 11 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.992Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeMon Jul 21 2025 00:00:00 GMT+0000 (UTC), EndTimeThu Aug 21 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.995Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeFri Aug 01 2025 00:00:00 GMT+0000 (UTC), EndTimeMon Sep 01 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"error","ts":"2025-08-07T09:44:39.997Z","caller":"server/runtime_javascript_logger.go:94","msg":"[===== SystemInit ===== (uid: )] [initialSeasonTournament] CurrentTime Thu Aug 07 2025 09:44:39 GMT+0000 (UTC), StartTimeMon Aug 11 2025 00:00:00 GMT+0000 (UTC), EndTimeThu Sep 11 2025 00:00:00 GMT+0000 (UTC)"}
{"level":"info","ts":"2025-08-07T09:44:39.998Z","caller":"server/storage_index.go:711","msg":"Initialized storage engine index","configuration":{"collection":"config","fields":["version"],"index_only":false,"key":"version","max_entries":1000,"name":"ConfigVersionIndex"}}
{"level":"info","ts":"2025-08-07T09:44:39.998Z","caller":"server/storage_index.go:711","msg":"Initialized storage engine index","configuration":{"collection":"mailbox","fields":["name"],"index_only":false,"key":"*","max_entries":100000000,"name":"MailboxIndex"}}
{"level":"info","ts":"2025-08-07T09:44:39.998Z","caller":"server/runtime_javascript.go:1774","msg":"JavaScript runtime modules loaded"}
{"level":"info","ts":"2025-08-07T09:44:39.998Z","caller":"server/runtime_javascript.go:1777","msg":"Allocating minimum JavaScript runtime pool","count":16}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime_javascript.go:1785","msg":"Allocated minimum JavaScript runtime pool"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:725","msg":"Found runtime modules","count":1,"modules":["index.js"]}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"reset_card_collection"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_send_mailbox_mail"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_send_mailbox_mail_global"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_check_mailbox_mail_global"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_config_index_query"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"search_username"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_get_version"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_config_local_cache_version_setup"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_test_generate_player_save_new_data"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"load_user_cards"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"version"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_update_debug_log"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_test_sync"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_generic_rpc_test"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"protorpc"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_update_config_node"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_update_config"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_system_reinit"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"swap_deck_card"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_get_config"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_get_player_info"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_test_leaderboard"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"upgrade_card"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"add_user_gems"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"handle_match_end"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_server_maintenance_cache_update"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_server_maintenance_cache_check"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_test"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_mailbox_mail_global_query"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"add_random_card"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_webhook_xsolla_user"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_import_account"}
{"level":"info","ts":"2025-08-07T09:44:40.388Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_webhook_xsolla"}
{"level":"info","ts":"2025-08-07T09:44:40.389Z","caller":"server/runtime.go:745","msg":"Registered JavaScript runtime RPC function invocation","id":"s2s_crm_get_nakama_notification"}
{"level":"info","ts":"2025-08-07T09:44:40.389Z","caller":"server/runtime.go:868","msg":"Registered JavaScript runtime Before function invocation","id":"deletegroup"}
{"level":"info","ts":"2025-08-07T09:44:40.389Z","caller":"server/runtime.go:1723","msg":"Registered JavaScript runtime After function invocation","id":"authenticateapple"}
{"level":"info","ts":"2025-08-07T09:44:40.389Z","caller":"server/runtime.go:1729","msg":"Registered JavaScript runtime After function invocation","id":"authenticatedevice"}
{"level":"info","ts":"2025-08-07T09:44:40.389Z","caller":"server/runtime.go:1735","msg":"Registered JavaScript runtime After function invocation","id":"authenticatefacebook"}
{"level":"info","ts":"2025-08-07T09:44:40.389Z","caller":"server/runtime.go:1756","msg":"Registered JavaScript runtime After function invocation","id":"addfriends"}
{"level":"info","ts":"2025-08-07T09:44:40.389Z","caller":"server/runtime.go:1780","msg":"Registered JavaScript runtime After function invocation","id":"joingroup"}
{"level":"info","ts":"2025-08-07T09:44:40.389Z","caller":"server/runtime.go:1783","msg":"Registered JavaScript runtime After function invocation","id":"leavegroup"}
{"level":"info","ts":"2025-08-07T09:44:40.389Z","caller":"server/runtime.go:1792","msg":"Registered JavaScript runtime After function invocation","id":"kickgroupusers"}
{"level":"info","ts":"2025-08-07T09:44:40.389Z","caller":"server/runtime.go:1795","msg":"Registered JavaScript runtime After function invocation","id":"promotegroupusers"}
{"level":"info","ts":"2025-08-07T09:44:40.389Z","caller":"server/runtime.go:2637","msg":"Registered JavaScript runtime Tournament End function invocation"}
{"level":"info","ts":"2025-08-07T09:44:40.390Z","caller":"server/runtime.go:2650","msg":"Registered JavaScript runtime Tournament Reset function invocation"}
{"level":"info","ts":"2025-08-07T09:44:40.390Z","caller":"server/runtime.go:2663","msg":"Registered JavaScript runtime Leaderboard Reset function invocation"}
{"level":"info","ts":"2025-08-07T09:44:40.390Z","caller":"server/runtime.go:2702","msg":"Registered JavaScript runtime Purchase Notification Google function invocation"}
{"level":"info","ts":"2025-08-07T09:44:40.390Z","caller":"server/leaderboard_scheduler.go:109","msg":"Leaderboard scheduler start"}
{"level":"info","ts":"2025-08-07T09:44:40.390Z","caller":"server/leaderboard_scheduler.go:304","msg":"Leaderboard scheduler update","end_active":"62h15m19.609133086s","end_active_count":2,"expiry":"62h15m19.609133086s","expiry_count":2}
{"level":"info","ts":"2025-08-07T09:44:40.397Z","caller":"server/storage_index.go:401","msg":"Storage index loaded.","config":{"Name":"MailboxIndex","MaxEntries":100000000,"Collection":"mailbox","Key":"","Fields":["name"],"SortableFields":["name"],"IndexOnly":false,"Index":{}},"elapsed_time_ms":6}
{"level":"info","ts":"2025-08-07T09:44:40.411Z","caller":"server/storage_index.go:401","msg":"Storage index loaded.","config":{"Name":"ConfigVersionIndex","MaxEntries":1000,"Collection":"config","Key":"version","Fields":["version"],"SortableFields":["version"],"IndexOnly":false,"Index":{}},"elapsed_time_ms":13}

============================= 节点 IP 映射摘要 =============================
Node Record                                   Public IP            Private IP
---------------------------------------------------------------------------------------
prod-node1.nkmad-cluster.pwglab.com           ***********          ************
prod-node2.nkmad-cluster.pwglab.com           ************         ************
==============================================================================

节点首先启动, 列表只有一个 node, 就是自己

{"level":"info","ts":"2025-08-07T09:44:40.432Z","caller":"server/peer.go:1178","msg":"Updated services for cluster","nodes":["ad-95-61-mb1X"],"removed":[]}
{"level":"info","ts":"2025-08-07T09:44:40.432Z","caller":"server/peer.go:1024","msg":"Starting service watch for cluster"}
[DEBUG] memberlist: Initiating push/pull sync with:  ************:7335
[DEBUG] memberlist: Stream connection from=************:34802


{"level":"info","ts":"2025-08-07T09:44:40.433Z","caller":"server/api.go:157","msg":"Starting API server for gRPC requests","port":7349}
{"level":"info","ts":"2025-08-07T09:44:40.434Z","caller":"server/api.go:318","msg":"Starting API server gateway for HTTP requests","port":7350}
{"level":"info","ts":"2025-08-07T09:44:40.434Z","caller":"server/socket.go:102","msg":"Starting API server for TCP requests","port":7347}
{"level":"info","ts":"2025-08-07T09:44:40.435Z","caller":"server/peer.go:1178","msg":"Updated services for cluster","nodes":["ad-95-61-mb1X"],"removed":[]}
{"level":"info","ts":"2025-08-07T09:44:40.437Z","caller":"server/console.go:235","msg":"Starting Console server for gRPC requests","port":7348}
{"level":"info","ts":"2025-08-07T09:44:40.438Z","caller":"server/console.go:339","msg":"Starting Console server gateway for HTTP requests","port":7351}
{"level":"info","ts":"2025-08-07T09:44:40.439Z","caller":"main.go:254","msg":"Startup done","numMembers":1}

{"level":"info","ts":"2025-08-07T09:44:40.440Z","caller":"server/peer_leader.go:87","msg":"Became leader","node":"ad-95-61-mb1X"}
{"level":"info","ts":"2025-08-07T09:44:40.442Z","caller":"server/peer.go:1178","msg":"Updated services for cluster","nodes":["ad-95-61-mb1X"],"removed":[]}
{"level":"info","ts":"2025-08-07T09:44:40.449Z","caller":"server/peer_leader.go:114","msg":"Leadership update","isLeader":true}
{"level":"info","ts":"2025-08-07T09:44:40.451Z","caller":"server/peer.go:1178","msg":"Updated services for cluster","nodes":["ad-95-61-mb1X"],"removed":[]}
{"level":"warn","ts":"2025-08-07T09:44:44.435Z","caller":"server/peer_leader.go:118","msg":"Session expired during observe"}
{"level":"warn","ts":"2025-08-07T09:44:44.435Z","caller":"server/peer_leader.go:61","msg":"Session expired, reconnecting"}
{"level":"info","ts":"2025-08-07T09:44:44.438Z","caller":"server/peer.go:1178","msg":"Updated services for cluster","nodes":["ad-95-61-mb1X"],"removed":[]}
{"level":"info","ts":"2025-08-07T09:44:44.439Z","caller":"server/peer_leader.go:151","msg":"Session reconnected"}

两个节点同时处理节点增加
{"level":"info","ts":"2025-08-07T09:44:46.609Z","caller":"server/peer.go:1178","msg":"Updated services for cluster","nodes":["ad-3-251-Uabw","ad-95-61-mb1X"],"removed":[]}
[DEBUG] memberlist: Stream connection from=************:33396

{"level":"info","ts":"2025-08-07T09:44:46.620Z","caller":"server/peer.go:433","msg":"Node joined cluster","node":"ad-3-251-Uabw","address":"************:7335","status":1,"weight":0,"leader":false}
{"level":"info","ts":"2025-08-07T09:44:50.556Z","caller":"kit/client.go:321","msg":"shutdown completed","peer":"ad-95-61-mb1X"}
{"level":"info","ts":"2025-08-07T09:44:50.556Z","caller":"server/peer.go:1178","msg":"Updated services for cluster","nodes":["ad-3-251-Uabw"],"removed":["ad-95-61-mb1X"]}
{"level":"warn","ts":"2025-08-07T09:44:50.56227Z","logger":"etcd-client","caller":"v3@v3.5.14/retry_interceptor.go:63","msg":"retrying of unary invoker failed","target":"etcd-endpoints://0xc00673a3c0/etcd.prod-etcd.pwglab.com:2379","attempt":0,"error":"rpc error: code = NotFound desc = etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T09:44:50.562Z","caller":"server/peer_leader.go:167","msg":"Failed to update service","error":"etcdserver: requested lease not found"}
{"level":"info","ts":"2025-08-07T09:44:51.208Z","caller":"server/peer_leader.go:87","msg":"Became leader","node":"ad-95-61-mb1X"}
{"level":"warn","ts":"2025-08-07T09:44:51.212612Z","logger":"etcd-client","caller":"v3@v3.5.14/retry_interceptor.go:63","msg":"retrying of unary invoker failed","target":"etcd-endpoints://0xc00673a3c0/etcd.prod-etcd.pwglab.com:2379","attempt":0,"error":"rpc error: code = NotFound desc = etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T09:44:51.212Z","caller":"server/peer_leader.go:167","msg":"Failed to update service","error":"etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T09:44:51.638Z","caller":"server/peer_leader.go:118","msg":"Session expired during observe"}
{"level":"warn","ts":"2025-08-07T09:44:51.638Z","caller":"server/peer_leader.go:61","msg":"Session expired, reconnecting"}
{"level":"info","ts":"2025-08-07T09:44:51.642Z","caller":"server/peer_leader.go:151","msg":"Session reconnected"}
{"level":"info","ts":"2025-08-07T09:44:54.712Z","caller":"server/peer.go:1178","msg":"Updated services for cluster","nodes":["ad-3-251-Uabw"],"removed":[]}
{"level":"info","ts":"2025-08-07T09:44:55.419Z","caller":"server/peer.go:1178","msg":"Updated services for cluster","nodes":["ad-3-251-Uabw"],"removed":[]}
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:52680
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:35130
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:55750
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:35046
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:36382
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:39208
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:34116
[DEBUG] memberlist: Stream connection from=************:36300
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
{"level":"info","ts":"2025-08-07T09:50:09.338Z","caller":"server/runtime_javascript_logger.go:74","msg":"crmGetVersion payload: \"{}\"","rpc_id":"s2s_get_version"}
[DEBUG] memberlist: Stream connection from=************:60534
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:36594
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:49988
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:38256
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:33390
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:52414
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:38412
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
[DEBUG] memberlist: Stream connection from=************:36516
[DEBUG] memberlist: Initiating push/pull sync with: ad-3-251-Uabw ************:7335
