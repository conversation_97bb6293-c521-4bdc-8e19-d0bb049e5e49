{"level":"warn","ts":"2025-08-07T10:21:44.950Z","caller":"server/peer_leader.go:61","msg":"Session expired, reconnecting"}
{"level":"info","ts":"2025-08-07T10:21:44.955Z","caller":"server/peer_leader.go:151","msg":"Session reconnected"}
[DEBUG] memberlist: Initiating push/pull sync with: ad-95-61-mb1X 172.31.95.61:7335
{"level":"warn","ts":"2025-08-07T10:21:54.518Z","caller":"server/peer_leader.go:167","msg":"Failed to update service","error":"etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T10:21:54.518318Z","logger":"etcd-client","caller":"v3@v3.5.14/retry_interceptor.go:63","msg":"retrying of unary invoker failed","target":"etcd-endpoints://0xc0054d8780/etcd.prod-etcd.pwglab.com:2379","attempt":0,"error":"rpc error: code = NotFound desc = etcdserver: requested lease not found"}
{"level":"info","ts":"2025-08-07T10:21:55.150Z","caller":"server/peer_leader.go:87","msg":"Became leader","node":"ad-38-120-bnsp"}
{"level":"warn","ts":"2025-08-07T10:21:55.154Z","caller":"server/peer_leader.go:167","msg":"Failed to update service","error":"etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T10:21:55.154155Z","logger":"etcd-client","caller":"v3@v3.5.14/retry_interceptor.go:63","msg":"retrying of unary invoker failed","target":"etcd-endpoints://0xc0054d8780/etcd.prod-etcd.pwglab.com:2379","attempt":0,"error":"rpc error: code = NotFound desc = etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T10:21:55.950Z","caller":"server/peer_leader.go:118","msg":"Session expired during observe"}
{"level":"warn","ts":"2025-08-07T10:21:55.950Z","caller":"server/peer_leader.go:61","msg":"Session expired, reconnecting"}
{"level":"info","ts":"2025-08-07T10:21:55.954Z","caller":"server/peer_leader.go:151","msg":"Session reconnected"}
[DEBUG] memberlist: Stream connection from=172.31.95.61:52016
{"level":"warn","ts":"2025-08-07T10:22:01.521977Z","logger":"etcd-client","caller":"v3@v3.5.14/retry_interceptor.go:63","msg":"retrying of unary invoker failed","target":"etcd-endpoints://0xc0054d8780/etcd.prod-etcd.pwglab.com:2379","attempt":0,"error":"rpc error: code = NotFound desc = etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T10:22:01.522Z","caller":"server/peer_leader.go:167","msg":"Failed to update service","error":"etcdserver: requested lease not found"}
{"level":"info","ts":"2025-08-07T10:22:02.150Z","caller":"server/peer_leader.go:87","msg":"Became leader","node":"ad-38-120-bnsp"}
{"level":"warn","ts":"2025-08-07T10:22:02.155Z","caller":"server/peer_leader.go:167","msg":"Failed to update service","error":"etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T10:22:02.15548Z","logger":"etcd-client","caller":"v3@v3.5.14/retry_interceptor.go:63","msg":"retrying of unary invoker failed","target":"etcd-endpoints://0xc0054d8780/etcd.prod-etcd.pwglab.com:2379","attempt":0,"error":"rpc error: code = NotFound desc = etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T10:22:02.750Z","caller":"server/peer_leader.go:118","msg":"Session expired during observe"}
{"level":"warn","ts":"2025-08-07T10:22:02.750Z","caller":"server/peer_leader.go:61","msg":"Session expired, reconnecting"}
{"level":"info","ts":"2025-08-07T10:22:02.754Z","caller":"server/peer_leader.go:151","msg":"Session reconnected"}
{"level":"warn","ts":"2025-08-07T10:22:06.517824Z","logger":"etcd-client","caller":"v3@v3.5.14/retry_interceptor.go:63","msg":"retrying of unary invoker failed","target":"etcd-endpoints://0xc0054d8780/etcd.prod-etcd.pwglab.com:2379","attempt":0,"error":"rpc error: code = NotFound desc = etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T10:22:06.517Z","caller":"server/peer_leader.go:167","msg":"Failed to update service","error":"etcdserver: requested lease not found"}
{"level":"info","ts":"2025-08-07T10:22:07.150Z","caller":"server/peer_leader.go:87","msg":"Became leader","node":"ad-38-120-bnsp"}
{"level":"warn","ts":"2025-08-07T10:22:07.159306Z","logger":"etcd-client","caller":"v3@v3.5.14/retry_interceptor.go:63","msg":"retrying of unary invoker failed","target":"etcd-endpoints://0xc0054d8780/etcd.prod-etcd.pwglab.com:2379","attempt":0,"error":"rpc error: code = NotFound desc = etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T10:22:07.159Z","caller":"server/peer_leader.go:167","msg":"Failed to update service","error":"etcdserver: requested lease not found"}
{"level":"info","ts":"2025-08-07T10:22:07.750Z","caller":"server/peer_leader.go:114","msg":"Leadership update","isLeader":true}
{"level":"warn","ts":"2025-08-07T10:22:07.755083Z","logger":"etcd-client","caller":"v3@v3.5.14/retry_interceptor.go:63","msg":"retrying of unary invoker failed","target":"etcd-endpoints://0xc0054d8780/etcd.prod-etcd.pwglab.com:2379","attempt":0,"error":"rpc error: code = NotFound desc = etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T10:22:07.755Z","caller":"server/peer_leader.go:167","msg":"Failed to update service","error":"etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T10:22:08.350Z","caller":"server/peer_leader.go:118","msg":"Session expired during observe"}
{"level":"warn","ts":"2025-08-07T10:22:08.350Z","caller":"server/peer_leader.go:61","msg":"Session expired, reconnecting"}
{"level":"info","ts":"2025-08-07T10:22:08.354Z","caller":"server/peer_leader.go:151","msg":"Session reconnected"}
{"level":"warn","ts":"2025-08-07T10:22:17.51815Z","logger":"etcd-client","caller":"v3@v3.5.14/retry_interceptor.go:63","msg":"retrying of unary invoker failed","target":"etcd-endpoints://0xc0054d8780/etcd.prod-etcd.pwglab.com:2379","attempt":0,"error":"rpc error: code = NotFound desc = etcdserver: requested lease not found"}
{"level":"warn","ts":"2025-08-07T10:22:17.518Z","caller":"server/peer_leader.go:167","msg":"Failed to update service","error":"etcdserver: requested lease not found"}

