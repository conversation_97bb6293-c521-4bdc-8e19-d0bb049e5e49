terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "remote" {
    organization = "funkylab"

    workspaces {
      name = "Nkmad_Prod_Env_Cluster"
    }
  }
}

provider "aws" {
  region = var.aws_region # 使用变量定义 AWS 区域
}

# 引用共享模块，假定其中包含 VPC、子网、Route 53 Zone 等共享资源
module "shared" {
  source   = "./shared"
  app_name = var.app_name_base
}

# 获取默认 VPC 信息
data "aws_vpc" "default" {
  default = true
}

#data "aws_subnet_ids" "default" {
#    vpc_id = data.aws_vpc.default.id
#}

data "aws_subnets" "default" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
}

// Subnet_id's in the order of Availability_zones
// https://github.com/hashicorp/terraform-provider-aws/issues/2120
// https://github.com/hashicorp/terraform-provider-aws/issues/3471
data "aws_subnet" "private" {
  count = length(data.aws_subnets.default.ids)
  id    = tolist(data.aws_subnets.default.ids)[count.index]
}

# 获取所有可用区
data "aws_availability_zones" "available" {
  state = "available"
}

locals {
  ids_sorted_by_az  = values(zipmap(data.aws_subnet.private.*.availability_zone, data.aws_subnet.private.*.id))
  cidr_sorted_by_az = values(zipmap(data.aws_subnet.private.*.availability_zone, data.aws_subnet.private.*.cidr_block))
}

locals {
  ami_name = "ubuntu-docker-server-${var.app_name_base}-prod-latest"
  key_name = "${var.app_name_base}_prod"

  selected_available_zones = [
    element(tolist(local.ids_sorted_by_az), 0),
    element(tolist(local.ids_sorted_by_az), 1),
    element(tolist(local.ids_sorted_by_az), 2)
  ]

  # 1. 在 locals 中调用 templatefile 函数一次，将渲染后的内容存入一个局部变量。
  #    (Call the templatefile function once within locals and store the rendered content in a local variable.)
  user_data_db_rpc_content = templatefile("${path.module}/user-data-rpc.sh.tftpl", {
    game_service_version = var.game_service_version
    use_rds_db           = var.use_rds_db
    db_address           = var.db_address
    node_index           = "" # rpc server random generate node index
    app                  = var.app_name_base
  })
}

data "aws_ami" "docker-server" {
  most_recent = true
  owners = [
    var.ami_owner
  ]
  filter {
    name = "name"
    values = [
      local.ami_name
    ]
  }
}

# ==============================================================================
# 安全组 (Security Groups)
# ==============================================================================

# NLB 安全组：允许所有必要的入站流量
resource "aws_security_group" "nakama_nlb_sg" {
  name        = "${var.app_name}-nakama-nlb-sg-prod"
  description = "Allow inbound traffic for Nakama NLB"
  vpc_id      = data.aws_vpc.default.id

  # 7350 (HTTP API)
  ingress {
    from_port   = 7350
    to_port     = 7350
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7351 (HTTP API - Embedded Developer Console)
  ingress {
    from_port   = 7351
    to_port     = 7351
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7349 (gRPC API)
  ingress {
    from_port   = 7349
    to_port     = 7349
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # 7335 (cluster peer communication)
  ingress {
    from_port   = 7335
    to_port     = 7335
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name    = "${var.app_name}-nakama-nlb-sg-prod"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
  }
}

# Nakama 实例安全组：允许来自 NLB 和 SSH 的流量
resource "aws_security_group" "nakama_instance_sg" {
  name        = "${var.app_name}-nakama-instance-sg-prod"
  description = "Allow inbound traffic from NLB and SSH for Nakama instances"
  vpc_id      = data.aws_vpc.default.id

  # 允许来自 NLB 的所有端口流量
  ingress {
    from_port       = 0
    to_port         = 0
    protocol        = "-1"
    security_groups = [aws_security_group.nakama_nlb_sg.id]
    description     = "Allow all traffic from NLB"
  }
  # --- 新增的规则 ---
  # 允许来自同一安全组内其他实例的流量（用于集群内部通信）
  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1" # "-1" 表示所有协议。您可以根据需要将其限制为特定的协议和端口
    self        = true # 这是关键！允许来自同一安全组的流量
    description = "Allow traffic from other instances in the same SG for clustering"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name    = "${var.app_name}-nakama-instance-sg-prod"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
  }
}

# ==============================================================================
# IAM 角色和策略 (IAM Roles and Policies)
# ==============================================================================

# EC2 实例的 IAM 角色，用于允许实例执行操作（例如上传 CloudWatch 指标）
resource "aws_iam_role" "app_prod_ec2_role" {
  name = "${var.app_name}-prod-ec2-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "",
        Effect = "Allow",
        Principal = {
          Service = [
            "ec2.amazonaws.com"
          ]
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# EC2 实例配置文件
resource "aws_iam_instance_profile" "app_prod_ec2_profile" {
  name = "${var.app_name}-prod-ec2-profile"
  role = aws_iam_role.app_prod_ec2_role.name
}

# CloudWatch Agent 自定义策略
resource "aws_iam_policy" "cloudwatch_agent_custom_policy" {
  name        = "${var.app_name}-CloudWatchAgentCustomPolicy"
  description = "Custom policy for CloudWatch Agent to send metrics and logs"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "cloudwatch:PutMetricData",
          "ec2:DescribeTags",
          "ec2:DescribeVolumes",
          "ec2:DescribeInstances"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:PutLogEvents",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutRetentionPolicy"
        ]
        Resource = "arn:aws:logs:${var.aws_region}:*:log-group:/aws/ec2/cloudwatch-agent/*"
      },
      # 新增的 S3 权限声明
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:ListBucket" # 如果需要列出桶中的对象，请添加此权限
        ]
        # 将 "your-config-bucket-name" 替换为实际的 S3 桶名称
        # 如果只需要特定文件，可以更精确地指定 Resource
        Resource = [
          "arn:aws:s3:::nakama-build",
          "arn:aws:s3:::nakama-build/*"
        ]
      }
    ]
  })
}

# 将 CloudWatch Agent 策略附加到 EC2 角色
resource "aws_iam_role_policy_attachment" "cloudwatch_agent_custom_policy_attach" {
  role       = aws_iam_role.app_prod_ec2_role.name
  policy_arn = aws_iam_policy.cloudwatch_agent_custom_policy.arn
}

# ==============================================================================
# Nakama DB RPC 服务器 (横向扩容 - Auto Scaling Group)
# ==============================================================================

# Launch Template for Nakama DB RPC instances
resource "aws_launch_template" "nakama_db_rpc_lt" {
  name_prefix   = "${var.app_name}-nakama-db-rpc-lt-"
  image_id      = data.aws_ami.docker-server.id
  instance_type = var.nakama_db_rpc_instance_type # 从变量获取实例类型
  key_name      = local.key_name
  vpc_security_group_ids = [
    aws_security_group.nakama_instance_sg.id,
    module.shared.app_shared_whitelist,
    /* connect to 1panel */
    module.shared.app_shared_allow_1panel,
    /* game api from everywhere */
    module.shared.app_shared_allow_all_api,
    module.shared.etcd_cluster_client_connection,
  ]
  iam_instance_profile {
    name = aws_iam_instance_profile.app_prod_ec2_profile.name
  }
  user_data = base64encode(local.user_data_db_rpc_content)

  credit_specification {
    cpu_credits = var.cpu_credits_type
  }

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name    = "${var.app_name}-nakama-db-rpc-prod"
      Service = "game"
      App     = var.app_name
      Env     = "prod"
      Role    = "DB_RPC"
    }
  }
}

# Auto Scaling Group for Nakama DB RPC
resource "aws_autoscaling_group" "nakama_db_rpc_asg" {
  name                = "${var.app_name}-nakama-db-rpc-asg-prod"
  max_size            = var.nakama_db_rpc_max_instances
  min_size            = var.nakama_db_rpc_min_instances
  desired_capacity    = var.nakama_db_rpc_desired_instances
  vpc_zone_identifier = local.selected_available_zones # 使用共享模块提供的私有子网
  target_group_arns = [
    # Auto Scaling 的实例只会加入到 73xx
    aws_lb_target_group.nakama_7350_tg.arn, # DB RPC 主要处理 7350 端口流量
    aws_lb_target_group.nakama_7351_tg.arn, # DB RPC 上的 7351 端口
    aws_lb_target_group.nakama_7349_tg.arn, # DB RPC 上的 7349 端口
  ]
  health_check_type         = "ELB"
  health_check_grace_period = 300  # 5 minutes
  force_delete              = true # 强制删除 ASG，即使有实例在运行

  launch_template {
    id      = aws_launch_template.nakama_db_rpc_lt.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "${var.app_name}-nakama-db-rpc-prod"
    propagate_at_launch = true
  }
  tag {
    key                 = "Service"
    value               = "game"
    propagate_at_launch = true
  }
  tag {
    key                 = "App"
    value               = var.app_name
    propagate_at_launch = true
  }
  tag {
    key                 = "Env"
    value               = "prod"
    propagate_at_launch = true
  }
  tag {
    key                 = "Role"
    value               = "DB_RPC"
    propagate_at_launch = true
  }
}

# 扩容策略：CPU 使用率
resource "aws_autoscaling_policy" "cpu_scaling_up" {
  name                   = "${var.app_name}-cpu-scaling-up"
  scaling_adjustment     = 1 # 增加 1 个实例
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "cpu_high" {
  alarm_name          = "${var.app_name}-cpu-high-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 60
  statistic           = "Average"
  threshold           = 90 # CPU 使用率达到 90%
  alarm_description   = "This alarm monitors EC2 CPU utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.cpu_scaling_up.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 扩容策略：内存使用率 (需要 CloudWatch Agent 上报)
resource "aws_autoscaling_policy" "memory_scaling_up" {
  name                   = "${var.app_name}-memory-scaling-up"
  scaling_adjustment     = 1
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "memory_high" {
  alarm_name          = "${var.app_name}-memory-high-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "mem_used_percent" # CloudWatch Agent 上报的内存指标
  namespace           = var.app_name       # CloudWatch Agent 的命名空间
  period              = 60
  statistic           = "Average"
  threshold           = 90 # 内存使用率达到 90%
  alarm_description   = "This alarm monitors EC2 memory utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.memory_scaling_up.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 扩容策略：磁盘使用率 (需要 CloudWatch Agent 上报)
resource "aws_autoscaling_policy" "disk_scaling_up" {
  name                   = "${var.app_name}-disk-scaling-up"
  scaling_adjustment     = 1
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "disk_high" {
  alarm_name          = "${var.app_name}-disk-high-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "disk_used_percent" # CloudWatch Agent 上报的磁盘指标
  namespace           = var.app_name        # CloudWatch Agent 的命名空间
  period              = 60
  statistic           = "Average"
  threshold           = 70 # 磁盘使用率达到 70%
  alarm_description   = "This alarm monitors EC2 disk utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.disk_scaling_up.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 缩容策略：CPU 使用率
resource "aws_autoscaling_policy" "cpu_scaling_down" {
  name                   = "${var.app_name}-cpu-scaling-down"
  scaling_adjustment     = -1 # 减少 1 个实例
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "cpu_low" {
  alarm_name          = "${var.app_name}-cpu-low-alarm"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 60
  statistic           = "Average"
  threshold           = 30 # CPU 使用率低于 30%
  alarm_description   = "This alarm monitors EC2 CPU utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.cpu_scaling_down.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 缩容策略：内存使用率
resource "aws_autoscaling_policy" "memory_scaling_down" {
  name                   = "${var.app_name}-memory-scaling-down"
  scaling_adjustment     = -1
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "memory_low" {
  alarm_name          = "${var.app_name}-memory-low-alarm"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "mem_used_percent"
  namespace           = var.app_name
  period              = 60
  statistic           = "Average"
  threshold           = 30 # 内存使用率低于 30%
  alarm_description   = "This alarm monitors EC2 memory utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.memory_scaling_down.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}


# ==============================================================================
# 网络负载均衡器 (Network Load Balancer - NLB)
# ==============================================================================

resource "aws_lb" "nakama_nlb" {
  name               = "${var.app_name}-nakama-nlb-prod"
  internal           = false
  ip_address_type    = "dualstack"
  load_balancer_type = "network"
  subnets            = local.selected_available_zones        # 使用共享模块提供的公共子网
  security_groups    = [aws_security_group.nakama_nlb_sg.id] # 附加 NLB 安全组

  enable_cross_zone_load_balancing = true # 启用跨可用区负载均衡
  tags = {
    Name    = "${var.app_name}-nakama-nlb-prod"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
  }
}

# ==============================================================================
# 目标组 (Target Groups)
# ==============================================================================

# 目标组：7350 -> 两套服务器的 7350 端口 (用于 DB RPC)
resource "aws_lb_target_group" "nakama_7350_tg" {
  name        = "${var.app_name}-nakama-7350-tg"
  port        = 7350
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance" # 目标类型为实例

  deregistration_delay = 300

  health_check {
    protocol            = "TCP"
    port                = 7350
    healthy_threshold   = 7
    unhealthy_threshold = 7
    timeout             = 10
    interval            = 30
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }
  tags = {
    Name = "${var.app_name}-nakama-7350-tg"
    App  = var.app_name
  }
}

# 目标组：7351 -> Nakama DB RPC 服务器的 7351 端口 (用于 Nakama Console)
resource "aws_lb_target_group" "nakama_7351_tg" {
  name        = "${var.app_name}-nakama-7351-tg"
  port        = 7351
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  deregistration_delay = 300

  health_check {
    protocol            = "TCP"
    port                = 7351
    healthy_threshold   = 7
    unhealthy_threshold = 7
    timeout             = 10
    interval            = 30
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }
  tags = {
    Name = "${var.app_name}-nakama-7351-tg"
    App  = var.app_name
  }
}

# 目标组：7349 -> 两套服务器的 7349 端口
resource "aws_lb_target_group" "nakama_7349_tg" {
  name        = "${var.app_name}-nakama-7349-tg"
  port        = 7349
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  deregistration_delay = 300

  health_check {
    protocol            = "TCP"
    port                = 7349
    healthy_threshold   = 7
    unhealthy_threshold = 7
    timeout             = 10
    interval            = 30
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }
  tags = {
    Name = "${var.app_name}-nakama-7349-tg"
    App  = var.app_name
  }
}

# ==============================================================================
# NLB 监听器 (NLB Listeners)
# ==============================================================================

# 监听器：7350 -> 两套服务器的 7350 端口 (用于 DB RPC)
resource "aws_lb_listener" "nakama_7350_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7350
  protocol          = "TLS" # 修正：改为 TLS
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7350_tg.arn
  }
}

# 监听器：7351 -> Nakama DB RPC 服务器的 7351 端口 (用于 Nakama Console)
resource "aws_lb_listener" "nakama_7351_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7351
  protocol          = "TLS" # 修正：改为 TLS
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7351_tg.arn
  }
}

# 监听器：7349 -> 两套服务器的 7349 端口
resource "aws_lb_listener" "nakama_7349_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7349
  protocol          = "TLS" # 修正：改为 TLS
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7349_tg.arn
  }
}

# ==============================================================================
# Route 53 DNS 记录 (Route 53 DNS Records)
# ==============================================================================

# 为 NLB 创建 Route 53 A 记录 (主入口)
resource "aws_route53_record" "nakama_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "prod.${var.app_name}.${module.shared.zone_name}" # 例如: nakama.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 Console 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_console_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "console.${var.app_name}.${module.shared.zone_name}" # 例如: console.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}
# 动态添加 node 的 record 备用
resource "aws_route53_record" "nakama_node_record" {
  count   = var.nakama_db_rpc_max_instances
  zone_id = module.shared.zone_id
  name    = "prod-node${count.index + 1}.${var.app_name}.${module.shared.zone_name}"
  type    = "A"

  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }

  allow_overwrite = true
}

# 为 NLB 的 gRPC 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_grpc_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "grpc.${var.app_name}.${module.shared.zone_name}" # 例如: grpc.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# outputs.tf

output "nakama_nlb_dns_name" {
  description = "Nakama NLB 的 DNS 名称"
  value       = aws_lb.nakama_nlb.dns_name
}

output "nakama_api_url" {
  description = "Nakama HTTP API (DB RPC) 的 URL"
  value       = "https://${aws_route53_record.nakama_nlb_record.fqdn}:7350"
}

output "nakama_db_rpc_console_url" {
  description = "Nakama DB RPC Console 的 URL"
  value       = "https://${aws_route53_record.nakama_console_nlb_record.fqdn}:7351"
}

output "nakama_grpc_url" {
  description = "Nakama gRPC API 的 URL"
  value       = "https://${aws_route53_record.nakama_grpc_nlb_record.fqdn}:7349"
}

output "game_service_version" {
  value       = var.game_service_version
  description = "game_service_version which can be used for next steps"
}