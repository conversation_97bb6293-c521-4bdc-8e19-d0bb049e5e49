#!/bin/bash
set -eux

echo "2025-07-11:10-44-50"
echo "Start to run user-data.sh "
echo " ====================Game=================="
pwd
cd /home/<USER>
whoami
#echo " =========================================="
#echo " check the environment variables
#echo " =========================================="
#su ubuntu -c "source .bashrc && echo ""$NAME"""
#su ubuntu -c "source .bashrc && echo ""$APP"""
#echo
echo
echo " =========================================="
echo " assign the input to var and check"
echo " =========================================="
game_service_version="${game_service_version}"
use_rds_db="${use_rds_db}"
db_address="${db_address}"
app="${app}"
node_index="${node_index}"
# 检查 node_index 是否存在或为空
#if [ -z "${node_index}" ]; then
#  # 如果 node_index 不存在或为空，则生成一个8个字符的随机字符串
#  node_index=$(head /dev/urandom | tr -dc A-Za-z0-9 | head -c 8)
#  echo "node_index set to random string：${node_index}"
#fi

## 检查 db_address 是否存在或为空
#if [ -z "${db_address}" ]; then
#  # 如果 db_address 不存在或为空，则使用环境变量 COCKROACH_DB_CLOUD_PROD_ADDRESS
#  # shellcheck disable=SC2157
#  if [ -n "$$COCKROACH_DB_CLOUD_PROD_ADDRESS" ]; then
#    db_address="$$COCKROACH_DB_CLOUD_PROD_ADDRESS"
#    echo "db_address 未设置，已从环境变量 COCKROACH_DB_CLOUD_PROD_ADDRESS 获取值：${db_address}"
#  else
#    echo "db_address 未设置，且环境变量 COCKROACH_DB_CLOUD_PROD_ADDRESS 也不存在。请手动设置 db_address 或 COCKROACH_DB_CLOUD_PROD_ADDRESS。"
#  fi
#fi

echo "game_service_version: ${game_service_version}"
echo "node_index: ${node_index}"
echo "use_rds_db: ${use_rds_db}"
echo "db_address: ${db_address}"
echo "app: ${app}"
echo
echo
echo " =========================================="
echo " download the docker-compose file"
echo " =========================================="
su ubuntu -c "cd /home/<USER>/home/<USER>/.bashrc && aws s3 cp \"s3://nakama-build/${app}/composer/prod/cluster/docker-compose.yml\" \"docker-compose.yml\""
echo
echo

# --- 新增代码块 开始 ---
echo " =========================================="
echo " create a helper script to generate node id"
echo " =========================================="
# 使用 quoted heredoc (<< 'EOL') 将脚本内容原样写入文件，避免转义问题
su ubuntu -c "cat > /home/<USER>/generate-node-id.sh" << 'EOL'
#!/bin/bash
set -e
TOKEN=$(curl -s -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")
IP_PART=$(curl -s -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/local-ipv4 | awk -F. '{print $3"-"$4}')
RAND_PART=$(head /dev/urandom | tr -dc A-Za-z0-9 | head -c 4)
# 【关键修改】使用 $$ 转义，以防止 Terraform 尝试解析这些变量
echo "$${IP_PART}-$${RAND_PART}"
EOL

# 赋予帮助脚本执行权限
su ubuntu -c "chmod +x /home/<USER>/generate-node-id.sh"
# --- 新增代码块 结束 ---


echo " =========================================="
echo " star to run docker compose"
echo " =========================================="
# --- 修改后的 docker compose 命令 ---
su ubuntu -c "cd /home/<USER>/home/<USER>/.bashrc && ./docker-login.sh && export DB_ADDRESS=${db_address} && export NAKAMA_BACKEND_TAG=${game_service_version} && export NAKAMA_PROD_NODE_ID=\$(./generate-node-id.sh) && docker compose up -d"

sleep 10
echo
echo