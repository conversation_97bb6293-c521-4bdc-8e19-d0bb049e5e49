# 新版 cluster

## ip 地址的问题

su ubuntu -c "cd /home/<USER>/home/<USER>/.bashrc && ./docker-login.sh && export DB_ADDRESS=${db_address} && export NAKAMA_BACKEND_TAG=${game_service_version} && export NAKAMA_PROD_NODE_ID=$(head /dev/urandom | tr -dc A-Za-z0-9 | head -c 8) && docker compose up -d"

```bash
export DB_ADDRESS=ltGqqg1Hka:<EMAIL>:26257/defaultdb?sslmode=verify-full && export NAKAMA_BACKEND_TAG=0.1.17-cluster-1 && export NAKAMA_PROD_NODE_ID=$(TOKEN=$(curl -s -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600") && curl -s -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/local-ipv4 | tr '.' '-') && docker compose up -d

export DB_ADDRESS=ltGqqg1Hka:<EMAIL>:26257/defaultdb?sslmode=verify-full && export NAKAMA_BACKEND_TAG=0.1.17-cluster-1 && export NAKAMA_PROD_NODE_ID=1 && docker compose up -d
```


```bash
&& export NAKAMA_PROD_NODE_ID=$(TOKEN=$(curl -s -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600"); echo "$(curl -s -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/local-ipv4 | awk -F. '{print $3"-"$4}')-$(head /dev/urandom | tr -dc A-Za-z0-9 | head -c 4)") && docker compose up -d

```


## etcd 的访问问题

我现在有一个  etcd cluster, 该 etcd 使用 internet-face 的 NLB 监听 3 个节点的 etcd 服务. 

etcd-nlb 配置了一个 sg, 名字是 etcd-sg, 我不希望所有都能访问这个 nlb, 所以加了一些 IP白名单, 同时我希望某些通过 ASG 自动创建的 EC2 也可以访问这个 NLB, 

etcd 的所有 ec2 节点也配置了同样的 etcd-sg.

我目前的设计是, 

1. 创建一个新的 sg, etcd-client-sg
2. 在 etcd-sg 增加一个 inbound, 允许 etcd-client-sg 访问端口 2379
3. 然后我在 ASG 的 launch template 中加入 etcd-client-sg
4. 所有通过 ASG 启动的 EC2, 自动加入 etcd-client-sg
5. 我期待启动的 EC2 是不是可以访问 etcd-nlb, 可以访问端口 2379

但是实际测试不工作, EC2 上访问 etcd 的 public ip 节点 2379 也不工作, private ip 节点 2379 工作.

这里有什么问题吗?


-----

# 旧版部分 cluster 方案

## 如何更新多节点 config

1. 直接向 realtime 节点发送 config update 请求, 更新 DB
2. 其他节点从 DB 同步到本地缓存 


## 系统构建提示词

1. 我需要构建一套 Nakama 服务器的生产环境, 该环境包含两套 Nakama 服务器
2. 两套 Nakama 服务器需要通过 NLB 进行负载均衡.
3. 第一套 Nakama 服务器成为 Nakama Realtime 服务器, 由于我们使用 nakama opensource, 我们只能使用 1 个EC2 来部署, 但是我们需要支持纵向扩容, 比如如果单节点 t3.large 出现了占用率过高, 我们需要启动一个高一个级别的 instance 比如t3.xlarge 来替换它.
4. 第二套 Nakama 服务器我们称为 Nakama DB Rpc 服务器, 其主要功能是处理 RPC, 这些 RPC 之和 DB 交互, 可以无限横向扩展. 该服务器需要用 Auto Scaling 来进行管理, 当 CPU 达到 90%, Memory 达到 90%, Disk 达到 70% 时, 我们需要启动 开始扩容
5. 每个服务器节点都要暴露如下端口 ssh 22,  7350, 7351, 7349 和 9100 还有一个是 1Panel 的服务端口 24914
端口用途
   HTTP API server on port 7350. Port can be changed in the config.
   HTTP API server powering the embedded developer console on port 7351. Port can be changed in the config. 
   gRPC API server on port 7349. Port is chosen based on the API server port
   9100 Prometheus metrics
   1Panel 服务端口 24914
6. 我们 Load balance 需要提供多个端口映射 
   1. **7350** -> 两套服务器的 7350 端口 >>> 这个端口将用于 DB RPC 
   2. **7450** -> Nakama Realtime 服务器的 7350 端口 >>> 这个端口将用于 Realtime 功能 
   3. 7351 -> Nakama DB Rpc 服务器的 7351  >>> 这个端口将用于 Nakama Console, 尽可能降低 Realtime 的负载 
   4. 7451 -> Nakama Realtime 服务器的 7351 端口 >>>  用于特殊的使用 
   5. 7349 -> 两套服务器的 7349 端口 
   6. 7449 -> Nakama Realtime 服务器的 7349 端口
7. Load Balance 的端口暴露都要支持 SSL, 其证书使用我们根域名 pwglab.com 的证书, 可以用通配, 未来我们的访问域名都为 xxx.pwglab.com 的形式.
8. 关于自动伸缩的需求如下
   1. 需要一个永久固定节点支持 realtime 服务 
   2. 需要一个自动扩展的组支持 rpc 
   3. 支持 realtime 的永久节点也可以支持 RPC 
   4. 所以自动扩展的 rpc 组, 需要将 realtime 服务器性能考虑进来, 整体评估是否需要扩展, 但是扩展只会增加或减少新的 用于 rpc 的节点, realtime 的节点永远不变 
   5. realtime 的节点可以通过其他机制实现半自动纵向扩展, 比如如果发现内存不够了, 出发一套代码, 创建一个内存更高的节点, 然后将 load balance 的映射到这个新的节点, 再把旧的节点 down.
9. 每个节点需要有权限运行 cwagent 监听 CPU, Memory, Disk 的使用情况, 并将这些数据上报到 CloudWatch. 配置已经有了, 只需要配置 role 给 ec2 权限上传 metric
10. 我需要你提供 Terraform 的代码,  
11. 我的服务是 docker compose, 需要使用预定的 docker compose yaml 文件来启动服务 

## 关于 Realtime 节点的更新

> 问题: 这里 match 是完全在内存中维护的, 节点一旦重启, 当前正在进行的 match 全部会丢失, 无法继续. 所以我们需要考虑如何实现 realtime 节点的更新, 目前的思路更新节点前必须没有 match 在进行.
> 
> 1. 增加 pvp 的 cooldown 时间
> 2. 增加配置, 可以在某段时间只会有 bot pvp, 直到所有的 realtime 都结束
> 3. 增加维护时间, 在维护时间内, pvp realtime 不可用
> 4. match 服务器单独节点, 降低更新频率



有两种思路

1. 直接重启, 有 downtime
2. 先启动一个节点, 然后将 load balance 的映射到这个新的节点, 再把旧的节点 down. 这种方式对于实时功能的状态有些不确定, 比如 B 替换 A, 客户端连接 A, 当我们启动 B 的时候, 客户可能还在和 A 进行交互, 这些交互如果是在内存中存储, 那么这些数据就无法体现在 B 的内存中. 所以当切换为 B 之后, 客户端和 B 之间的交互可能出现不可预测状态

实时功能

1. 目前有 notification, 这个功能主要是实时接收 notification, 这个目前理论是有 fallback 的, 就是下次登陆会主动获取所有 notification, 这个可以测试一下, 应该是从 DB 中读取
   1. 测试方法, 启动两个节点, 连接同一个 DB
   2. 客户端启动连接 node1
   3. 向 node1 发送 notification, 客户端直接收到
   4. 向 node2 发送 notification, 客户端无法直接收到
   5. node1 -> node2
   6. 客户端重新登陆, 可以收到 node2 的 notification
2. leaderboard, 写入 record, list leaderboard 等操作
   1. 测试方法, 启动两个节点, 连接同一个 DB
   2. 客户端A启动 连接 node1
   3. 客户端B启动 连接 node2
   4. 客户端A send record 到 node1
   5. 客户端B list leaderboard, 可以应该是无法看到客户端A的记录
   6. 客户端A list leaderboard, 可以应该是看到客户端A的记录
   7. 如果将 node1 -> node2, 客户端再 list leaderboard, 能看到自己的记录变化吗? 
   8. 什么情况可以实现客户端B看到客户端A的记录? 重启 node2 应该是可以, 是否还有非 downtime 的重启可以实现?
3. 未来的 realtime pvp 的 match
   1. 测试方法, 启动两个节点, 连接同一个 DB
   2. 客户端A启动 连接 node1
   3. 客户端B启动 连接 node1
   4. 客户端A 和 B 建立的 match, 这个模拟了 node2 启动期间, node1 仍然工作, 并且创建了 match.
   5. 这时候如果将 node1 -> node2, 是否可以实现 match 的无缝切换? 最主要的是 客户端A和 B 的 match 是否还能继续?
4. 公会聊天 chat
   1. 测试方法, 启动两个节点, 连接同一个 DB
   2. 客户端A启动 连接 node1
   3. 客户端B启动 连接 node1
   4. A 发送消息
   5. B 发送消息
   6. node1 -> node2

```bash
#su ubuntu -c "cd /home/<USER>/home/<USER>/.bashrc && ./docker-login.sh && export DB_ADDRESS=ltGqqg1Hka:<EMAIL>:26257/defaultdb?sslmode=verify-full && export NAKAMA_BACKEND_TAG=0.1.4 && export NAKAMA_PROD_NODE_ID=0 && docker compose up -d"
```