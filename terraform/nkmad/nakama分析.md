https://deepwiki.com/heroiclabs/nakama/1.1-architecture-overview






https://deepwiki.com/search/_f5acdc8e-b7b3-4e5a-a711-ba0065540a7d


这里应该不是本地缓存机制, 我这里想找到是其内置的缓存, 用于其 realtime 功能, 比如可能用于这些功能 chat, match , leaderboard 还有 notification, 基于这个信息,帮我分析一下, 这 4 个功能是否依赖某些内存数据从而达到实时效果, 这些机制如果启动两个 nakama 节点, 其信息应该是无法直接互通, 我想清楚的知道都有哪些数据被这些内存维护了, 从而让我能够清楚的了解自己部署的多节点服务器有哪些功能无法跨节点正常工作.

nakama 对于 cluser 是闭源收费, 应该有额外的组件来跨节点同步这些信息. 未来我们想基于开源自研这套 cluster 方案. 