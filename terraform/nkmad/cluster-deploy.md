# 真正 cluster 部署

基于 nakama-plus 实现真正的 cluster 部署

## etcd cluster

构建一个  etcd cluster 用于服务发现

```mermaid
graph TD
    subgraph "AWS Region: us-east-1"
        subgraph VPC [VPC: 10.0.0.0/16]
            subgraph "App Subnets"
                AppNode1[应用节点 1]
                AppNode2[应用节点 2]
            end

            subgraph AZ1 [可用区: us-east-1a]
                subgraph Subnet1 [私有子网 1]
                    ASG1-->Node1(EC2: t3.small<br>etcd 节点 1)
                end
            end

            subgraph AZ2 [可用区: us-east-1b]
                subgraph Subnet2 [私有子网 2]
                    ASG2-->Node2(EC2: t3.small<br>etcd 节点 2)
                end
            end

            subgraph AZ3 [可用区: us-east-1c]
                subgraph Subnet3 [私有子网 3]
                    ASG3-->Node3(EC2: t3.small<br>etcd 节点 3)
                end
            end

            subgraph Endpoints [VPC Endpoints]
                S3Endpoint[S3 Gateway Endpoint]
                EC2Endpoint[EC2 Interface Endpoint]
            end

            %% 定义连接
            AppNode1 -- "客户端请求 (2379)" --> Node1
            AppNode1 -- "客户端请求 (2379)" --> Node2
            AppNode1 -- "客户端请求 (2379)" --> Node3

            Node1 <--> |"Peer 通信 (2380)"| Node2
            Node2 <--> |"Peer 通信 (2380)"| Node3
            Node3 <--> |"Peer 通信 (2380)"| Node1

            Node1 -- "API 调用" --> EC2Endpoint
            Node1 -- "下载安装包" --> S3Endpoint
        end
    end

    style AppNode1 fill:#cde4ff
    style AppNode2 fill:#cde4ff

```