# ---------------------------------------------------------------------------------------------------------------------
# REQUIRED PARAMETERS
# You must provide a value for each of these parameters.
# ---------------------------------------------------------------------------------------------------------------------
/* Required update for different app */
variable "app_name" {
  description = ""
  default     = "nkmad"
}

/* check the backend project docker tags */
variable "game_service_version" {
  description = ""
  default     = "0.1.0"
}

// ======================================================
/* Optional update for different app */
variable "instance_type" {
  description = "The type of EC2 Instances to run (e.g. t2.micro)"
  type        = string
  default     = "t3.medium"
}

variable "cpu_credits_type" {
  description = "The name to use for EC2 instance"
  type        = string
  default     = "standard"
}

variable "ami_owner" {
  description = "Filter for the AMI owner"
  default     = "self"
}

/*
0 -> use cloud db
1 -> use RDS db
*/
variable "use_rds_db" {
  description = ""
  default     = 0
}

variable "node_index" {
  description = ""
  default     = 0
}

variable "db_address" {
  description = "specific db address"
  default     = ""
}

variable "live_game_subdomain" {
  description = "the live game subdomain"
  // default = "live-game"
  default = "live-game"
}

variable "suffix" {
  description = "suffix"
  default     = ""
}

variable "aws_region" {
  description = "AWS 区域"
  type        = string
  default     = "us-east-1"
}


variable "acm_certificate_arn" {
  description = "AWS Certificate Manager (ACM) 中 *.pwglab.com 通配符证书的 ARN"
  type        = string
  # 示例：arn:aws:acm:us-east-1:123456789012:certificate/abcdefgh-1234-5678-9012-abcdefghijkl
  # *.nkmad.pwglab.com
  # https://us-east-1.console.aws.amazon.com/acm/home?region=us-east-1#/certificates/223e374a-d40b-4a64-8630-daca66546db7
  default = "arn:aws:acm:us-east-1:817064356605:certificate/223e374a-d40b-4a64-8630-daca66546db7" # 请替换为您的证书 ARN
}

variable "nakama_realtime_instance_type" {
  description = "Nakama Realtime 服务器的 EC2 实例类型"
  type        = string
  default     = "t3.large" # 初始实例类型
}

variable "nakama_db_rpc_instance_type" {
  description = "Nakama DB RPC 服务器的 EC2 实例类型"
  type        = string
  default     = "t3.medium" # ASG 实例类型
}

variable "nakama_db_rpc_min_instances" {
  description = "Nakama DB RPC Auto Scaling Group 的最小实例数"
  type        = number
  default     = 1
}

variable "nakama_db_rpc_max_instances" {
  description = "Nakama DB RPC Auto Scaling Group 的最大实例数"
  type        = number
  default     = 3 # 可以根据需求调整
}

variable "nakama_db_rpc_desired_instances" {
  description = "Nakama DB RPC Auto Scaling Group 的期望实例数"
  type        = number
  default     = 1
}