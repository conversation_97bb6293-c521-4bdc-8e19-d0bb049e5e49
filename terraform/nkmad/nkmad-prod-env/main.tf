terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "remote" {
    organization = "funkylab"

    workspaces {
      name = "Nkmad_Prod_Env"
    }
  }
}

provider "aws" {
  region = var.aws_region # 使用变量定义 AWS 区域
}

# 引用共享模块，假定其中包含 VPC、子网、Route 53 Zone 等共享资源
module "shared" {
  source   = "./shared"
  app_name = var.app_name
}

# 获取默认 VPC 信息
data "aws_vpc" "default" {
  default = true
}

#data "aws_subnet_ids" "default" {
#    vpc_id = data.aws_vpc.default.id
#}

data "aws_subnets" "default" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
}

// Subnet_id's in the order of Availability_zones
// https://github.com/hashicorp/terraform-provider-aws/issues/2120
// https://github.com/hashicorp/terraform-provider-aws/issues/3471
data "aws_subnet" "private" {
  count = length(data.aws_subnets.default.ids)
  id    = tolist(data.aws_subnets.default.ids)[count.index]
}

# 获取所有可用区
data "aws_availability_zones" "available" {
  state = "available"
}

locals {
  ids_sorted_by_az  = values(zipmap(data.aws_subnet.private.*.availability_zone, data.aws_subnet.private.*.id))
  cidr_sorted_by_az = values(zipmap(data.aws_subnet.private.*.availability_zone, data.aws_subnet.private.*.cidr_block))
}

locals {
  ami_name = "ubuntu-docker-server-${var.app_name}-prod-latest"
  key_name = "${var.app_name}_prod"

  selected_available_zones = [
    element(tolist(local.ids_sorted_by_az), 0),
    element(tolist(local.ids_sorted_by_az), 1),
    element(tolist(local.ids_sorted_by_az), 2)
  ]
}

data "aws_ami" "docker-server" {
  most_recent = true
  owners = [
    var.ami_owner
  ]
  filter {
    name = "name"
    values = [
      local.ami_name
    ]
  }
}

# ==============================================================================
# 安全组 (Security Groups)
# ==============================================================================

# NLB 安全组：允许所有必要的入站流量
resource "aws_security_group" "nakama_nlb_sg" {
  name        = "${var.app_name}-nakama-nlb-sg-prod"
  description = "Allow inbound traffic for Nakama NLB"
  vpc_id      = data.aws_vpc.default.id

  # 7350 (HTTP API)
  ingress {
    from_port   = 7350
    to_port     = 7350
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7450 (Realtime HTTP API)
  ingress {
    from_port   = 7450
    to_port     = 7450
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7351 (HTTP API - Embedded Developer Console)
  ingress {
    from_port   = 7351
    to_port     = 7351
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7451 (Realtime HTTP API - Special Console)
  ingress {
    from_port   = 7451
    to_port     = 7451
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7349 (gRPC API)
  ingress {
    from_port   = 7349
    to_port     = 7349
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  # 7449 (Realtime gRPC API)
  ingress {
    from_port   = 7449
    to_port     = 7449
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name    = "${var.app_name}-nakama-nlb-sg-prod"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
  }
}

# Nakama 实例安全组：允许来自 NLB 和 SSH 的流量
resource "aws_security_group" "nakama_instance_sg" {
  name        = "${var.app_name}-nakama-instance-sg-prod"
  description = "Allow inbound traffic from NLB and SSH for Nakama instances"
  vpc_id      = data.aws_vpc.default.id

  # 允许来自 NLB 的所有端口流量
  ingress {
    from_port       = 0
    to_port         = 0
    protocol        = "-1"
    security_groups = [aws_security_group.nakama_nlb_sg.id]
    description     = "Allow all traffic from NLB"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name    = "${var.app_name}-nakama-instance-sg-prod"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
  }
}

# ==============================================================================
# IAM 角色和策略 (IAM Roles and Policies)
# ==============================================================================

# EC2 实例的 IAM 角色，用于允许实例执行操作（例如上传 CloudWatch 指标）
resource "aws_iam_role" "app_prod_ec2_role" {
  name = "${var.app_name}-prod-ec2-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "",
        Effect = "Allow",
        Principal = {
          Service = [
            "ec2.amazonaws.com"
          ]
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# EC2 实例配置文件
resource "aws_iam_instance_profile" "app_prod_ec2_profile" {
  name = "${var.app_name}-prod-ec2-profile"
  role = aws_iam_role.app_prod_ec2_role.name
}

# CloudWatch Agent 自定义策略
resource "aws_iam_policy" "cloudwatch_agent_custom_policy" {
  name        = "${var.app_name}-CloudWatchAgentCustomPolicy"
  description = "Custom policy for CloudWatch Agent to send metrics and logs"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "cloudwatch:PutMetricData",
          "ec2:DescribeTags",
          "ec2:DescribeVolumes",
          "ec2:DescribeInstances"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:PutLogEvents",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutRetentionPolicy"
        ]
        Resource = "arn:aws:logs:${var.aws_region}:*:log-group:/aws/ec2/cloudwatch-agent/*"
      },
      # 新增的 S3 权限声明
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:ListBucket" # 如果需要列出桶中的对象，请添加此权限
        ]
        # 将 "your-config-bucket-name" 替换为实际的 S3 桶名称
        # 如果只需要特定文件，可以更精确地指定 Resource
        Resource = [
          "arn:aws:s3:::nakama-build",
          "arn:aws:s3:::nakama-build/*"
        ]
      }
    ]
  })
}

# 将 CloudWatch Agent 策略附加到 EC2 角色
resource "aws_iam_role_policy_attachment" "cloudwatch_agent_custom_policy_attach" {
  role       = aws_iam_role.app_prod_ec2_role.name
  policy_arn = aws_iam_policy.cloudwatch_agent_custom_policy.arn
}

# ========================================================
resource "aws_instance" "nakama_realtime_server" {
  ami           = data.aws_ami.docker-server.id
  instance_type = var.nakama_realtime_instance_type # 从变量获取实例类型


  vpc_security_group_ids = [
    aws_security_group.nakama_instance_sg.id,
    # module.shared.app_shared_allow_ssh,
    module.shared.app_shared_whitelist,
    /* whitelist to console */
    module.shared.app_shared_whitelist_http_console,
    /* connect to 1panel */
    module.shared.app_shared_allow_1panel,
    # 未来可以移除
    /* game api from everywhere */
    module.shared.app_shared_allow_all_api,
  ]
  # 使用本地变量中的第一个子网 ID
  subnet_id = tolist(local.ids_sorted_by_az)[0]

  # 根据子网的可用区来设置实例的可用区
  # 假设 local.cidr_sorted_by_az 的键仍然是可用区，值是 CIDR
  # 您可能需要调整 locals 以便直接获取可用区
  # 更直接的做法是获取子网数据源的 availability_zone 属性
  # availability_zone = data.aws_subnet.private[0].availability_zone # 假设您有一个数据源数组

  user_data = data.template_file.user_data_realtime.rendered
  key_name  = local.key_name
  tags = {
    Name    = "${var.app_name}-nakama-realtime-prod"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
    Node    = var.node_index
  }
  /*
  The credit_specification block supports the following:
  cpu_credits - (Optional) The credit option for CPU usage. Can be "standard" or "unlimited".
  T3 instances are launched as unlimited by default. T2 instances are launched as standard by default.
  */
  credit_specification {
    cpu_credits = var.cpu_credits_type
  }
  iam_instance_profile = aws_iam_instance_profile.app_prod_ec2_profile.name # 引用你上面定义的实例配置文件
}

data "template_file" "user_data_realtime" {
  template = file("user-data-realtime.sh")
  vars = {
    # 添加一个随机字符串，强制 Terraform 识别 user_data 变化
    # _force_update        = timestamp()
    # realtime 的节点的版本固定为 0.1.5, terraform 重新应用的时候不能更新这个 instance
    game_service_version = "0.1.5"
    use_rds_db           = var.use_rds_db
    node_index           = 0 # realtime server always use 0
    db_address           = var.db_address
    app                  = var.app_name
  }
}
data "template_file" "user_data_db_rpc" {
  template = file("user-data-rpc.sh")
  vars = {
    # 添加一个随机字符串，强制 Terraform 识别 user_data 变化
    # _force_update        = timestamp()
    game_service_version = var.game_service_version
    use_rds_db           = var.use_rds_db
    db_address           = var.db_address
    node_index           = "" # rpc server random generate node index
    app                  = var.app_name
  }
}

resource "aws_eip" "nakama_realtime_eip" {
  domain   = "vpc"
  instance = aws_instance.nakama_realtime_server.id
  tags = {
    Name = "${var.app_name}-nakama-realtime-eip-prod"
    App  = var.app_name
  }
}

/* seem like change the ip TF cannot recognise, need to modify manually */
resource "aws_route53_record" "nakama_realtime_record" {
  zone_id = module.shared.zone_id
  name    = "realtime.${var.app_name}.${module.shared.zone_name}"
  type    = "A"
  ttl     = "300"
  records = [
    aws_eip.nakama_realtime_eip.public_ip
  ]
  allow_overwrite = true
}

output "realtime_ec2_ip" {
  value = aws_eip.nakama_realtime_eip.public_ip
}

# ==============================================================================
# Nakama DB RPC 服务器 (横向扩容 - Auto Scaling Group)
# ==============================================================================

# Launch Template for Nakama DB RPC instances
resource "aws_launch_template" "nakama_db_rpc_lt" {
  name_prefix   = "${var.app_name}-nakama-db-rpc-lt-"
  image_id      = data.aws_ami.docker-server.id
  instance_type = var.nakama_db_rpc_instance_type # 从变量获取实例类型
  key_name      = local.key_name
  vpc_security_group_ids = [
    aws_security_group.nakama_instance_sg.id,
    module.shared.app_shared_whitelist,
    /* whitelist to console */
    module.shared.app_shared_whitelist_http_console,
    /* connect to 1panel */
    module.shared.app_shared_allow_1panel,
    # 未来可以移除
    /* game api from everywhere */
    module.shared.app_shared_allow_all_api,
  ]
  iam_instance_profile {
    name = aws_iam_instance_profile.app_prod_ec2_profile.name
  }
  user_data = base64encode(data.template_file.user_data_db_rpc.rendered)

  credit_specification {
    cpu_credits = var.cpu_credits_type
  }

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name    = "${var.app_name}-nakama-db-rpc-prod"
      Service = "game"
      App     = var.app_name
      Env     = "prod"
      Role    = "DB_RPC"
    }
  }
}

# Auto Scaling Group for Nakama DB RPC
resource "aws_autoscaling_group" "nakama_db_rpc_asg" {
  name                = "${var.app_name}-nakama-db-rpc-asg-prod"
  max_size            = var.nakama_db_rpc_max_instances
  min_size            = var.nakama_db_rpc_min_instances
  desired_capacity    = var.nakama_db_rpc_desired_instances
  vpc_zone_identifier = local.selected_available_zones # 使用共享模块提供的私有子网
  target_group_arns = [
    # Auto Scaling 的实例只会加入到 73xx, NLB 74xx 都只会到 realtime 的 独立 instance
    aws_lb_target_group.nakama_7350_tg.arn, # DB RPC 主要处理 7350 端口流量
    aws_lb_target_group.nakama_7351_tg.arn, # DB RPC 上的 7351 端口
    aws_lb_target_group.nakama_7349_tg.arn, # DB RPC 上的 7349 端口
    # 另外连个口不加入到 NLB, NLB 直接访问 realtime 的端口
    # aws_lb_target_group.nakama_9100_tg.arn, # Prometheus 端口
    # aws_lb_target_group.nakama_24914_tg.arn # 1Panel 端口
  ]
  health_check_type         = "ELB"
  health_check_grace_period = 300  # 5 minutes
  force_delete              = true # 强制删除 ASG，即使有实例在运行

  launch_template {
    id      = aws_launch_template.nakama_db_rpc_lt.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "${var.app_name}-nakama-db-rpc-prod"
    propagate_at_launch = true
  }
  tag {
    key                 = "Service"
    value               = "game"
    propagate_at_launch = true
  }
  tag {
    key                 = "App"
    value               = var.app_name
    propagate_at_launch = true
  }
  tag {
    key                 = "Env"
    value               = "prod"
    propagate_at_launch = true
  }
  tag {
    key                 = "Role"
    value               = "DB_RPC"
    propagate_at_launch = true
  }
}

# 扩容策略：CPU 使用率
resource "aws_autoscaling_policy" "cpu_scaling_up" {
  name                   = "${var.app_name}-cpu-scaling-up"
  scaling_adjustment     = 1 # 增加 1 个实例
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "cpu_high" {
  alarm_name          = "${var.app_name}-cpu-high-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 60
  statistic           = "Average"
  threshold           = 90 # CPU 使用率达到 90%
  alarm_description   = "This alarm monitors EC2 CPU utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.cpu_scaling_up.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 扩容策略：内存使用率 (需要 CloudWatch Agent 上报)
resource "aws_autoscaling_policy" "memory_scaling_up" {
  name                   = "${var.app_name}-memory-scaling-up"
  scaling_adjustment     = 1
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "memory_high" {
  alarm_name          = "${var.app_name}-memory-high-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "mem_used_percent" # CloudWatch Agent 上报的内存指标
  namespace           = var.app_name       # CloudWatch Agent 的命名空间
  period              = 60
  statistic           = "Average"
  threshold           = 90 # 内存使用率达到 90%
  alarm_description   = "This alarm monitors EC2 memory utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.memory_scaling_up.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 扩容策略：磁盘使用率 (需要 CloudWatch Agent 上报)
resource "aws_autoscaling_policy" "disk_scaling_up" {
  name                   = "${var.app_name}-disk-scaling-up"
  scaling_adjustment     = 1
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "disk_high" {
  alarm_name          = "${var.app_name}-disk-high-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "disk_used_percent" # CloudWatch Agent 上报的磁盘指标
  namespace           = var.app_name        # CloudWatch Agent 的命名空间
  period              = 60
  statistic           = "Average"
  threshold           = 70 # 磁盘使用率达到 70%
  alarm_description   = "This alarm monitors EC2 disk utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.disk_scaling_up.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 缩容策略：CPU 使用率
resource "aws_autoscaling_policy" "cpu_scaling_down" {
  name                   = "${var.app_name}-cpu-scaling-down"
  scaling_adjustment     = -1 # 减少 1 个实例
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "cpu_low" {
  alarm_name          = "${var.app_name}-cpu-low-alarm"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 60
  statistic           = "Average"
  threshold           = 30 # CPU 使用率低于 30%
  alarm_description   = "This alarm monitors EC2 CPU utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.cpu_scaling_down.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}

# 缩容策略：内存使用率
resource "aws_autoscaling_policy" "memory_scaling_down" {
  name                   = "${var.app_name}-memory-scaling-down"
  scaling_adjustment     = -1
  cooldown               = 300
  adjustment_type        = "ChangeInCapacity"
  autoscaling_group_name = aws_autoscaling_group.nakama_db_rpc_asg.name
}

resource "aws_cloudwatch_metric_alarm" "memory_low" {
  alarm_name          = "${var.app_name}-memory-low-alarm"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "mem_used_percent"
  namespace           = var.app_name
  period              = 60
  statistic           = "Average"
  threshold           = 30 # 内存使用率低于 30%
  alarm_description   = "This alarm monitors EC2 memory utilization"
  actions_enabled     = true
  alarm_actions       = [aws_autoscaling_policy.memory_scaling_down.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.nakama_db_rpc_asg.name
  }
}


# ==============================================================================
# 网络负载均衡器 (Network Load Balancer - NLB)
# ==============================================================================

resource "aws_lb" "nakama_nlb" {
  name               = "${var.app_name}-nakama-nlb-prod"
  internal           = false
  ip_address_type    = "dualstack"
  load_balancer_type = "network"
  subnets            = local.selected_available_zones        # 使用共享模块提供的公共子网
  security_groups    = [aws_security_group.nakama_nlb_sg.id] # 附加 NLB 安全组

  enable_cross_zone_load_balancing = true # 启用跨可用区负载均衡
  tags = {
    Name    = "${var.app_name}-nakama-nlb-prod"
    Service = "game"
    App     = var.app_name
    Env     = "prod"
  }
}

# ==============================================================================
# 目标组 (Target Groups)
# ==============================================================================

# 目标组：7350 -> 两套服务器的 7350 端口 (用于 DB RPC)
resource "aws_lb_target_group" "nakama_7350_tg" {
  name        = "${var.app_name}-nakama-7350-tg"
  port        = 7350
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance" # 目标类型为实例

  deregistration_delay = 300

  health_check {
    protocol            = "TCP"
    port                = 7350
    healthy_threshold   = 7
    unhealthy_threshold = 7
    timeout             = 10
    interval            = 30
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }
  tags = {
    Name = "${var.app_name}-nakama-7350-tg"
    App  = var.app_name
  }
}

# 目标组：7450 -> Nakama Realtime 服务器的 7350 端口 (用于 Realtime 功能)
resource "aws_lb_target_group" "nakama_7450_tg" {
  name                 = "${var.app_name}-nakama-7450-tg"
  port                 = 7350 # 实例上的目标端口
  protocol             = "TCP"
  vpc_id               = data.aws_vpc.default.id
  target_type          = "instance"
  deregistration_delay = 300

  health_check {
    protocol            = "TCP"
    port                = 7350
    healthy_threshold   = 7
    unhealthy_threshold = 7
    timeout             = 10
    interval            = 30
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }

  tags = {
    Name = "${var.app_name}-nakama-7450-tg"
    App  = var.app_name
  }
}

# 目标组：7351 -> Nakama DB RPC 服务器的 7351 端口 (用于 Nakama Console)
resource "aws_lb_target_group" "nakama_7351_tg" {
  name        = "${var.app_name}-nakama-7351-tg"
  port        = 7351
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  deregistration_delay = 300

  health_check {
    protocol            = "TCP"
    port                = 7351
    healthy_threshold   = 7
    unhealthy_threshold = 7
    timeout             = 10
    interval            = 30
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }
  tags = {
    Name = "${var.app_name}-nakama-7351-tg"
    App  = var.app_name
  }
}

# 目标组：7451 -> Nakama Realtime 服务器的 7351 端口 (用于特殊用途)
resource "aws_lb_target_group" "nakama_7451_tg" {
  name        = "${var.app_name}-nakama-7451-tg"
  port        = 7351 # 实例上的目标端口
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  deregistration_delay = 300

  health_check {
    protocol            = "TCP"
    port                = 7351
    healthy_threshold   = 7
    unhealthy_threshold = 7
    timeout             = 10
    interval            = 30
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }
  tags = {
    Name = "${var.app_name}-nakama-7451-tg"
    App  = var.app_name
  }
}

# 目标组：7349 -> 两套服务器的 7349 端口
resource "aws_lb_target_group" "nakama_7349_tg" {
  name        = "${var.app_name}-nakama-7349-tg"
  port        = 7349
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  deregistration_delay = 300

  health_check {
    protocol            = "TCP"
    port                = 7349
    healthy_threshold   = 7
    unhealthy_threshold = 7
    timeout             = 10
    interval            = 30
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }
  tags = {
    Name = "${var.app_name}-nakama-7349-tg"
    App  = var.app_name
  }
}

# 目标组：7449 -> Nakama Realtime 服务器的 7349 端口
resource "aws_lb_target_group" "nakama_7449_tg" {
  name        = "${var.app_name}-nakama-7449-tg"
  port        = 7349 # 实例上的目标端口
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  deregistration_delay = 300

  health_check {
    protocol            = "TCP"
    port                = 7349
    healthy_threshold   = 7
    unhealthy_threshold = 7
    timeout             = 10
    interval            = 30
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }
  tags = {
    Name = "${var.app_name}-nakama-7449-tg"
    App  = var.app_name
  }
}

# 目标组：9100 -> Prometheus Metrics
resource "aws_lb_target_group" "nakama_9100_tg" {
  name        = "${var.app_name}-nakama-9100-tg"
  port        = 9100
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  deregistration_delay = 300

  health_check {
    protocol            = "TCP"
    port                = 9100
    healthy_threshold   = 7
    unhealthy_threshold = 7
    timeout             = 10
    interval            = 30
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }
  tags = {
    Name = "${var.app_name}-nakama-9100-tg"
  }
}

# 目标组：24914 -> 1Panel 服务端口
resource "aws_lb_target_group" "nakama_24914_tg" {
  name        = "${var.app_name}-nakama-24914-tg"
  port        = 24914
  protocol    = "TCP"
  vpc_id      = data.aws_vpc.default.id
  target_type = "instance"

  deregistration_delay = 300

  health_check {
    protocol            = "TCP"
    port                = 24914
    healthy_threshold   = 7
    unhealthy_threshold = 7
    timeout             = 10
    interval            = 30
  }

  stickiness {
    enabled = true
    type    = "source_ip"
  }
  tags = {
    Name = "${var.app_name}-nakama-24914-tg"
  }
}


# ==============================================================================
# NLB 监听器 (NLB Listeners)
# ==============================================================================

# 监听器：7350 -> 两套服务器的 7350 端口 (用于 DB RPC)
resource "aws_lb_listener" "nakama_7350_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7350
  protocol          = "TLS" # 修正：改为 TLS
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7350_tg.arn
  }
}

# 监听器：7450 -> Nakama Realtime 服务器的 7350 端口 (用于 Realtime 功能)
resource "aws_lb_listener" "nakama_7450_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7450
  protocol          = "TLS" # 修正：改为 TLS
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7450_tg.arn
  }
}

# 监听器：7351 -> Nakama DB RPC 服务器的 7351 端口 (用于 Nakama Console)
resource "aws_lb_listener" "nakama_7351_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7351
  protocol          = "TLS" # 修正：改为 TLS
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7351_tg.arn
  }
}

# 监听器：7451 -> Nakama Realtime 服务器的 7351 端口 (用于特殊用途)
resource "aws_lb_listener" "nakama_7451_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7451
  protocol          = "TLS" # 修正：改为 TLS
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7451_tg.arn
  }
}

# 监听器：7349 -> 两套服务器的 7349 端口
resource "aws_lb_listener" "nakama_7349_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7349
  protocol          = "TLS" # 修正：改为 TLS
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7349_tg.arn
  }
}

# 监听器：7449 -> Nakama Realtime 服务器的 7349 端口
resource "aws_lb_listener" "nakama_7449_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 7449
  protocol          = "TLS" # 修正：改为 TLS
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_7449_tg.arn
  }
}

# 监听器：9100 -> Prometheus Metrics (如果需要通过 NLB 暴露)
resource "aws_lb_listener" "nakama_9100_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 9100
  protocol          = "TLS" # 修正：改为 TLS
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_9100_tg.arn
  }
}

# 监听器：24914 -> 1Panel 服务端口 (如果需要通过 NLB 暴露)
resource "aws_lb_listener" "nakama_24914_listener" {
  load_balancer_arn = aws_lb.nakama_nlb.arn
  port              = 24914
  protocol          = "TLS" # 修正：改为 TLS
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nakama_24914_tg.arn
  }
}

# ==============================================================================
# 目标组注册 (Target Group Attachments)
# ==============================================================================

# 将 Nakama Realtime 服务器注册到相应的目标组
resource "aws_lb_target_group_attachment" "realtime_7450_attachment" {
  target_group_arn = aws_lb_target_group.nakama_7450_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 7350
}

resource "aws_lb_target_group_attachment" "realtime_7451_attachment" {
  target_group_arn = aws_lb_target_group.nakama_7451_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 7351
}

resource "aws_lb_target_group_attachment" "realtime_7449_attachment" {
  target_group_arn = aws_lb_target_group.nakama_7449_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 7349
}

# 将 Nakama Realtime 服务器也注册到共享的 7350/7349/9100/24914 目标组
resource "aws_lb_target_group_attachment" "realtime_7350_attachment" {
  target_group_arn = aws_lb_target_group.nakama_7350_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 7350
}

resource "aws_lb_target_group_attachment" "realtime_7349_attachment" {
  target_group_arn = aws_lb_target_group.nakama_7349_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 7349
}

resource "aws_lb_target_group_attachment" "realtime_9100_attachment" {
  target_group_arn = aws_lb_target_group.nakama_9100_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 9100
}

resource "aws_lb_target_group_attachment" "realtime_24914_attachment" {
  target_group_arn = aws_lb_target_group.nakama_24914_tg.arn
  target_id        = aws_instance.nakama_realtime_server.id
  port             = 24914
}


# ==============================================================================
# Route 53 DNS 记录 (Route 53 DNS Records)
# ==============================================================================

# 为 NLB 创建 Route 53 A 记录 (主入口)
resource "aws_route53_record" "nakama_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "prod.${var.app_name}.${module.shared.zone_name}" # 例如: nakama.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

resource "aws_route53_record" "nakama_node1_record" {
  zone_id = module.shared.zone_id
  name    = "prod-node1.${var.app_name}.${module.shared.zone_name}" # 例如: nakama.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

resource "aws_route53_record" "nakama_node2_record" {
  zone_id = module.shared.zone_id
  name    = "prod-node2.${var.app_name}.${module.shared.zone_name}" # 例如: nakama.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

resource "aws_route53_record" "nakama_node3_record" {
  zone_id = module.shared.zone_id
  name    = "prod-node3.${var.app_name}.${module.shared.zone_name}" # 例如: nakama.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 Realtime 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_realtime_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "realtime-api.${var.app_name}.${module.shared.zone_name}" # 例如: realtime-api.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 Console 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_console_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "console.${var.app_name}.${module.shared.zone_name}" # 例如: console.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 Realtime Console 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_realtime_console_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "realtime-console.${var.app_name}.${module.shared.zone_name}" # 例如: realtime-console.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 gRPC 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_grpc_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "grpc.${var.app_name}.${module.shared.zone_name}" # 例如: grpc.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 Realtime gRPC 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_realtime_grpc_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "realtime-grpc.${var.app_name}.${module.shared.zone_name}" # 例如: realtime-grpc.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 Prometheus 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_prometheus_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "prometheus.${var.app_name}.${module.shared.zone_name}" # 例如: prometheus.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}

# 为 NLB 的 1Panel 端口创建 Route 53 A 记录
resource "aws_route53_record" "nakama_onepanel_nlb_record" {
  zone_id = module.shared.zone_id
  name    = "1panel.${var.app_name}.${module.shared.zone_name}" # 例如: onepanel.mygame.pwglab.com
  type    = "A"
  alias {
    name                   = aws_lb.nakama_nlb.dns_name
    zone_id                = aws_lb.nakama_nlb.zone_id
    evaluate_target_health = true
  }
  allow_overwrite = true
}


# outputs.tf

output "nakama_realtime_public_ip" {
  description = "Nakama Realtime 服务器的弹性 IP 地址"
  value       = aws_eip.nakama_realtime_eip.public_ip
}

output "nakama_realtime_dns_name" {
  description = "Nakama Realtime 服务器的 DNS 名称"
  value       = aws_route53_record.nakama_realtime_record.fqdn
}

output "nakama_nlb_dns_name" {
  description = "Nakama NLB 的 DNS 名称"
  value       = aws_lb.nakama_nlb.dns_name
}

output "nakama_api_url" {
  description = "Nakama HTTP API (DB RPC) 的 URL"
  value       = "https://${aws_route53_record.nakama_nlb_record.fqdn}:7350"
}

output "nakama_realtime_api_url" {
  description = "Nakama Realtime HTTP API 的 URL"
  value       = "https://${aws_route53_record.nakama_realtime_nlb_record.fqdn}:7450"
}

output "nakama_db_rpc_console_url" {
  description = "Nakama DB RPC Console 的 URL"
  value       = "https://${aws_route53_record.nakama_console_nlb_record.fqdn}:7351"
}

output "nakama_realtime_console_url" {
  description = "Nakama Realtime Console 的 URL"
  value       = "https://${aws_route53_record.nakama_realtime_console_nlb_record.fqdn}:7451"
}

output "nakama_grpc_url" {
  description = "Nakama gRPC API 的 URL"
  value       = "https://${aws_route53_record.nakama_grpc_nlb_record.fqdn}:7349"
}

output "nakama_realtime_grpc_url" {
  description = "Nakama Realtime gRPC API 的 URL"
  value       = "https://${aws_route53_record.nakama_realtime_grpc_nlb_record.fqdn}:7449"
}

output "nakama_prometheus_url" {
  description = "Nakama Prometheus Metrics URL"
  value       = "https://${aws_route53_record.nakama_prometheus_nlb_record.fqdn}:9100"
}

output "nakama_onepanel_url" {
  description = "Nakama 1Panel URL"
  value       = "https://${aws_route53_record.nakama_onepanel_nlb_record.fqdn}:24914/tXTAGMf9P1"
}

output "game_service_version" {
  value       = var.game_service_version
  description = "game_service_version which can be used for next steps"
}