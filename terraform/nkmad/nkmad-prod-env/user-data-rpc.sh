#!/bin/bash
set -eux

echo "2025-07-11:10-44-50"
echo "Start to run user-data.sh "
echo " ====================Game=================="
pwd
cd /home/<USER>
whoami
#echo " =========================================="
#echo " check the environment variables
#echo " =========================================="
#su ubuntu -c "source .bashrc && echo ""$NAME"""
#su ubuntu -c "source .bashrc && echo ""$APP"""
#echo
echo
echo " =========================================="
echo " assign the input to var and check"
echo " =========================================="
game_service_version="${game_service_version}"
use_rds_db="${use_rds_db}"
db_address="${db_address}"
app="${app}"
node_index="${node_index}"
# 检查 node_index 是否存在或为空
#if [ -z "${node_index}" ]; then
#  # 如果 node_index 不存在或为空，则生成一个8个字符的随机字符串
#  node_index=$(head /dev/urandom | tr -dc A-Za-z0-9 | head -c 8)
#  echo "node_index set to random string：${node_index}"
#fi

## 检查 db_address 是否存在或为空
#if [ -z "${db_address}" ]; then
#  # 如果 db_address 不存在或为空，则使用环境变量 COCKROACH_DB_CLOUD_PROD_ADDRESS
#  # shellcheck disable=SC2157
#  if [ -n "$$COCKROACH_DB_CLOUD_PROD_ADDRESS" ]; then
#    db_address="$$COCKROACH_DB_CLOUD_PROD_ADDRESS"
#    echo "db_address 未设置，已从环境变量 COCKROACH_DB_CLOUD_PROD_ADDRESS 获取值：${db_address}"
#  else
#    echo "db_address 未设置，且环境变量 COCKROACH_DB_CLOUD_PROD_ADDRESS 也不存在。请手动设置 db_address 或 COCKROACH_DB_CLOUD_PROD_ADDRESS。"
#  fi
#fi

echo "game_service_version: ${game_service_version}"
echo "node_index: ${node_index}"
echo "use_rds_db: ${use_rds_db}"
echo "db_address: ${db_address}"
echo "app: ${app}"
echo
echo
echo " =========================================="
echo " download the docker-compose file"
echo " =========================================="
su ubuntu -c "cd /home/<USER>/home/<USER>/.bashrc && aws s3 cp \"s3://nakama-build/${app}/composer/prod/docker-compose.yml\" \"docker-compose.yml\""
echo
echo

echo " =========================================="
echo " star to run docker compose"
echo " =========================================="
su ubuntu -c "cd /home/<USER>/home/<USER>/.bashrc && ./docker-login.sh && export DB_ADDRESS=${db_address} && export NAKAMA_BACKEND_TAG=${game_service_version} && export NAKAMA_PROD_NODE_ID=$(head /dev/urandom | tr -dc A-Za-z0-9 | head -c 8) && docker compose up -d"
#su ubuntu -c "cd /home/<USER>/home/<USER>/.bashrc && ./docker-login.sh && export DB_ADDRESS=ltGqqg1Hka:<EMAIL>:26257/defaultdb?sslmode=verify-full && export NAKAMA_BACKEND_TAG=0.1.4 && export NAKAMA_PROD_NODE_ID=0 && docker compose up -d"
sleep 10
echo
echo
