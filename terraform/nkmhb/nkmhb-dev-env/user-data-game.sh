#!/bin/bash
echo "Start to run user-data.sh "

echo " =========================================="
echo " ====================Game=================="
echo " =========================================="
pwd
cd /home/<USER>
who

echo " =========================================="
echo " Start to launch aws cloudwatch agent"
echo " =========================================="
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -s -c file:/home/<USER>/aws_cloudwatch_agent_config.json
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a status -m ec2 -s -c file:/home/<USER>/aws_cloudwatch_agent_config.json

app_name="nkmhb"

su ec2-user -c "aws s3 cp \"s3://nakama-build/$app_name/composer/dev/docker-compose.yml\" \"docker-compose.yml\""
su ec2-user -c "aws s3 cp s3://nakama-build/$app_name/composer/dev/data/ data/ --recursive"

sudo service docker status
sudo service docker start
sudo service docker status
whoami
su ec2-user -c "./docker-login.sh && source .bashrc && export NAKAMA_BACKEND_TAG=${game_service_version} && docker compose up -d"
# clean up the env data
su ec2-user -c "sed -i "/export COCKROACH_DB_CLOUD_PROD_.*=.*/d" ~/.bashrc"
su ec2-user -c "sed -i "/export RDS_POSTGRESQL_PROD_.*=.*/d" ~/.bashrc"
su ec2-user -c "sed -i "/export CR_PAT=.*/d" ~/.bashrc"



