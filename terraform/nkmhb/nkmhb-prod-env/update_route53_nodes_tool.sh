#!/bin/bash

# Set default values for parameters
TARGET_GROUP_ARN="${1:-arn:aws:elasticloadbalancing:us-east-1:817064356605:targetgroup/nakamabase-prod-7349/e144ccde4d49cb56}"
HOSTED_ZONE_ID="${2:-Z06363652DU4H3FACSAFC}"
APP_NAME="${3:-nakamabase}"

# Initialize counter
counter=1

# Get instance IDs from the specified Target Group
instance_ids=$(aws elbv2 describe-target-health --target-group-arn "$TARGET_GROUP_ARN" --query 'TargetHealthDescriptions[].Target.Id' --output text)
echo "instance_ids: $instance_ids"
# Loop through instance IDs, get Public IPv4 addresses, and update Route 53
for instance_id in $instance_ids; do
    echo "===> start to process instance: $instance_id"
    public_ip=$(aws ec2 describe-instances --instance-ids "$instance_id" --query 'Reservations[].Instances[].PublicIpAddress' --output text)

    # Construct Route 53 Entry and set IP address
    entry="live-game-odd.node$counter.$APP_NAME.pwglab.com"
    echo "Updating Route 53 entry: $entry with IP: $public_ip"
    aws route53 change-resource-record-sets \
        --hosted-zone-id "$HOSTED_ZONE_ID" \
        --change-batch "{
            \"Changes\": [
                {
                    \"Action\": \"UPSERT\",
                    \"ResourceRecordSet\": {
                        \"Name\": \"$entry\",
                        \"Type\": \"A\",
                        \"TTL\": 300,
                        \"ResourceRecords\": [
                            {\"Value\": \"$public_ip\"}
                        ]
                    }
                }
            ]
        }"

    # Increment counter
    ((counter++))
done

echo "Route 53 Entries for $APP_NAME have been updated."
