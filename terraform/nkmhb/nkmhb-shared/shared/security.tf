data "aws_vpc" "default" {
  default = true
}
provider "aws" {
  region = "us-east-1"
}

locals {
  app_name = var.app_name
}

resource "aws_security_group" "app_shared_allow_ssh" {
  name        = "${local.app_name}_shared_allow_ssh"
  description = "Allow SSH inbound traffic to ${local.app_name}"

  ingress {
    description      = "SSH 22 from VPC"
    from_port        = 22
    to_port          = 22
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "${local.app_name}_allow_ssh"
    Env  = "prod"
    App  = local.app_name
  }
}

resource "aws_security_group" "app_shared_whitelist" {
  name        = "${local.app_name}_shared_whitelist"
  description = "Allow incoming for specified IP address to access ${local.app_name}"

  ingress {
    description = "cg purevpn xin"
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }

  ingress {
    description = "cg purevpn jack"
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }

  ingress {
    description = "pw office static IP"
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "pw aws lightsail london outline"
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "pw aws lightsail tokyo"
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "pw rm dev"
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "${local.app_name}_whitelist"
    Env  = "prod"
    App  = local.app_name
  }
}

resource "aws_security_group" "app_shared_allow_tls" {
  name        = "${local.app_name}_shared_allow_tls"
  description = "Allow TLS inbound traffic to ${local.app_name}"

  ingress {
    description      = "TLS from VPC"
    from_port        = 443
    to_port          = 443
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "${local.app_name}_allow_tls"
    Env  = "prod"
    App  = local.app_name
  }
}

resource "aws_security_group" "app_shared_allow_http_api" {
  name        = "${local.app_name}_shared_allow_http_api"
  description = "Allow HTTP API 7350 inbound traffic to ${local.app_name}"

  ingress {
    description      = "7350: the nakama HTTP API default port"
    from_port        = 7350
    to_port          = 7350
    protocol         = "udp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "7350: the nakama HTTP API default port"
    from_port        = 7350
    to_port          = 7350
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "${local.app_name}_shared_allow_http_api"
    Env  = "prod"
    App  = local.app_name
  }
}

resource "aws_security_group" "app_shared_allow_grpc_api" {
  name        = "${local.app_name}_shared_allow_grpc_api"
  description = "Allow gRPC API 7349 inbound traffic to ${local.app_name}"

  ingress {
    description      = "7349: nakama default gPRC API port"
    from_port        = 7349
    to_port          = 7349
    protocol         = "udp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "7349: nakama default gPRC API port"
    from_port        = 7349
    to_port          = 7349
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "${local.app_name}_shared_allow_grpc_api"
    Env  = "prod"
    App  = local.app_name
  }
}

resource "aws_security_group" "app_shared_whitelist_http_console" {
  name        = "${local.app_name}_shared_whitelist_http_console"
  description = "Allow HTTP console 7351 inbound traffic with whitelist"

  ingress {
    description = "cg purevpn xin"
    from_port   = 7351
    to_port     = 7351
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }

  ingress {
    description = "pw aws lightsail london outline"
    from_port   = 7351
    to_port     = 7351
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "pw aws lightsail tokyo"
    from_port   = 7351
    to_port     = 7351
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "pw rm dev"
    from_port   = 7351
    to_port     = 7351
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "${local.app_name}_shared_whitelist_http_console"
    Env  = "prod"
    App  = local.app_name
  }
}

resource "aws_security_group" "app_shared_allow_service" {
  name        = "${local.app_name}_shared_allow_service"
  description = "Allow service inbound traffic to ${local.app_name}"

  ingress {
    description      = "7349-7351"
    from_port        = 7349
    to_port          = 7351
    protocol         = "udp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "7349-7351"
    from_port        = 7349
    to_port          = 7351
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "9090"
    from_port        = 9090
    to_port          = 9090
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "${local.app_name}_shared_allow_service"
    Env  = "prod"
    App  = local.app_name
  }
}

resource "aws_security_group" "app_shared_allow_all_api" {
  name        = "${local.app_name}_shared_allow_all_api"
  description = "Allow all API 7349 and 7350 inbound traffic to ${local.app_name}"

  ingress {
    description      = "7349: nakama default gPRC API port"
    from_port        = 7349
    to_port          = 7349
    protocol         = "udp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "7349: nakama default gPRC API port"
    from_port        = 7349
    to_port          = 7349
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "7350: the nakama HTTP API default port"
    from_port        = 7350
    to_port          = 7350
    protocol         = "udp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "7350: the nakama HTTP API default port"
    from_port        = 7350
    to_port          = 7350
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "${local.app_name}_shared_allow_all_api"
    Env  = "prod"
    App  = local.app_name
  }
}

/* Security Group for RDS and EC2 communications */

# Define security group for RDS to allow EC2 inbound traffic
resource "aws_security_group" "app_rds_to_ec2_inbound_for_rds" {
  name        = "${local.app_name}_rds_to_ec2_inbound_for_rds"
  description = "Security group attached to RDS instance to allow EC2 instances with specific security groups attached to connect to the database. Modification could lead to connection loss."

  tags = {
    Name = "${local.app_name}_rds_to_ec2_inbound_for_rds"
    Env  = "prod"
    App  = local.app_name
  }
}

# Define security group for EC2 to allow outbound traffic to RDS
resource "aws_security_group" "app_ec2_to_rds_outbound_for_ec2" {
  name        = "${local.app_name}_ec2_to_rds_outbound_for_ec2"
  description = "Security group attached to instances to securely connect to RDS. Modification could lead to connection loss."

  tags = {
    Name = "${local.app_name}_ec2_to_rds_outbound_for_ec2"
    Env  = "prod"
    App  = local.app_name
  }
}

# Allow inbound traffic to app_rds_to_ec2_inbound_for_rds (rds) from app_ec2_to_rds_outbound_for_ec2 (ec2)
resource "aws_security_group_rule" "rds_ingress_from_ec2" {
  security_group_id        = aws_security_group.app_rds_to_ec2_inbound_for_rds.id
  type                     = "ingress"
  from_port                = 5432
  to_port                  = 5432
  protocol                 = "TCP"
  source_security_group_id = aws_security_group.app_ec2_to_rds_outbound_for_ec2.id
  description              = "Rule to allow connections from EC2 instances with corresponding sg attached"
}

# Allow outbound traffic from app_ec2_to_rds_outbound_for_ec2 (ec2) to app_rds_to_ec2_inbound_for_rds (rds)
resource "aws_security_group_rule" "ec2_egress_to_rds" {
  security_group_id        = aws_security_group.app_ec2_to_rds_outbound_for_ec2.id
  type                     = "egress"
  from_port                = 5432
  to_port                  = 5432
  protocol                 = "TCP"
  source_security_group_id = aws_security_group.app_rds_to_ec2_inbound_for_rds.id
  description              = "Rule to allow connections to RDS from any instances this security group is attached to"
}