terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 6.0"
    }
  }
  backend "remote" {
    organization = "funkylab"

    workspaces {
      name = "Nakama_Global"
    }
  }
}

locals {
  full_name = "${var.framework_name}_${var.app_name}"
}

provider "aws" {
  region = "us-east-1"
}

resource "aws_s3_bucket_versioning" "versioning_example" {
  bucket = aws_s3_bucket.nakama-build.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket" "nakama-build" {
  bucket = "nakama-build"
  tags = {
    App = "nakama"
    Env = "build"
  }
}

resource "aws_s3_bucket_public_access_block" "nakama-build" {
  bucket = aws_s3_bucket.nakama-build.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_object" "nakama-build" {
  for_each = fileset("${path.module}/nakama-build", "**/*")

  bucket = aws_s3_bucket.nakama-build.id
  key    = each.value
  source = "${path.module}/nakama-build/${each.value}"
}

module "vpc" {
  source = "terraform-aws-modules/vpc/aws"

  name = "${var.framework_name}-vpc"
  cidr = "10.0.0.0/16"

  azs             = ["us-east-1a", "us-east-1b", "us-east-1c"]
  public_subnets  = ["10.0.1.0/24", "10.0.2.0/24", "10.0.3.0/24"]
  private_subnets = ["10.0.11.0/24", "10.0.12.0/24", "10.0.13.0/24"]
  public_subnet_tags = {
    type = "public"
  }
  private_subnet_tags = {
    type = "private"
  }
  enable_nat_gateway = false
  enable_vpn_gateway = false

  tags = {
    Name          = "${var.framework_name}-vpc"
    Service       = "network"
    App           = var.app_name
    Env           = "prod"
    Terraform     = "true"
    FrameworkName = var.framework_name
  }
}

resource "tls_private_key" "dev-private-key" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

resource "tls_private_key" "prod-private-key" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

resource "aws_key_pair" "dev-key-pair" {
  key_name   = "${local.full_name}_dev"
  public_key = tls_private_key.dev-private-key.public_key_openssh
}

resource "aws_key_pair" "prod-key-pair" {
  key_name   = "${local.full_name}_prod"
  public_key = tls_private_key.prod-private-key.public_key_openssh
}

# 需要执行外部命令获取 pem 文件, 查看 Makefile
locals {
  private_key_pem_dev_path  = "../../../ci/${local.full_name}_dev.pem"
  private_key_pem_prod_path = "../../../ci/${local.full_name}_prod.pem"
}

resource "null_resource" "save_private_key_dev" {
  provisioner "local-exec" {
    command = "echo '${tls_private_key.dev-private-key.private_key_pem}'"
  }
}
resource "null_resource" "save_private_key_prod" {
  provisioner "local-exec" {
    command = "echo '${tls_private_key.prod-private-key.private_key_pem}'"
  }
}

output "private_key_pem_dev" {
  value     = tls_private_key.dev-private-key.private_key_pem
  sensitive = true
}

output "private_key_pem_prod" {
  value     = tls_private_key.prod-private-key.private_key_pem
  sensitive = true
}