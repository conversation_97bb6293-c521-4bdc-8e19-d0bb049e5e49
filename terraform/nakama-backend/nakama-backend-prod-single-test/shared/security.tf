data "aws_vpc" "default" {
  default = true
}
provider "aws" {
  region = "us-east-1"
}

locals {
  app_name = var.app_name
}

data "aws_security_group" "app_shared_allow_ssh" {
  name = "${local.app_name}_shared_allow_ssh"
}

data "aws_security_group" "app_shared_whitelist" {
  name = "${local.app_name}_shared_whitelist"
}

data "aws_security_group" "app_shared_allow_tls" {
  name = "${local.app_name}_shared_allow_tls"
}

data "aws_security_group" "app_shared_allow_http_api" {
  name = "${local.app_name}_shared_allow_http_api"
}

data "aws_security_group" "app_shared_allow_grpc_api" {
  name = "${local.app_name}_shared_allow_grpc_api"
}

data "aws_security_group" "app_shared_whitelist_http_console" {
  name = "${local.app_name}_shared_whitelist_http_console"
}

data "aws_security_group" "app_shared_allow_all_api" {
  name = "${local.app_name}_shared_allow_all_api"
}

data "aws_security_group" "app_shared_allow_service" {
  name = "${local.app_name}_shared_allow_service"
}

data "aws_security_group" "app_rds_to_ec2_inbound_for_rds" {
  name = "${local.app_name}_rds_to_ec2_inbound_for_rds"
}

data "aws_security_group" "app_ec2_to_rds_outbound_for_ec2" {
  name = "${local.app_name}_ec2_to_rds_outbound_for_ec2"
}
