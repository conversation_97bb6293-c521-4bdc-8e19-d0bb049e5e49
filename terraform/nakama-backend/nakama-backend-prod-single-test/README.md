# Prod Single Test

this is used for testing Prod single instance test


1. manually update the `game_service_version` and `use_rds_db` [tf cloud](https://app.terraform.io/app/funkylab/workspaces/Nakama_Backend_Prod_Single_Test/variables)
2. `game_service_version` the docker tag which wants to test
3. `use_rds_db` whether use RDS. 1 -> RDS, 0 -> Cockroachdb cloud
4. after  `terrafrom apply` successfully, check the link: http://prod-test.nakama-backend.pwglab.com:7351/#/users

For RDS: show user: `xinatrds`
For Cloud: show user: `xinatcloud`