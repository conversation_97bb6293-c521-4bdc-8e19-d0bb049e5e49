#!/bin/bash

# Get Target Group ARN from Terraform output
TARGET_GROUP_ARN=$(terraform output -raw target_group_7349_arn)

# Get other parameters from Terraform output
HOSTED_ZONE_ID=$(terraform output -raw  hosted_zone_id)
APP_NAME=$(terraform output -raw app_name)
echo "===> Start to update route53 with $TARGET_GROUP_ARN and $HOSTED_ZONE_ID. app name is $APP_NAME"
# Rest of your script remains the same
./update_route53_nodes_tool.sh "$TARGET_GROUP_ARN" "$HOSTED_ZONE_ID" "$APP_NAME"