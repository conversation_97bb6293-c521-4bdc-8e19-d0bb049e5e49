# ---------------------------------------------------------------------------------------------------------------------
# REQUIRED PARAMETERS
# You must provide a value for each of these parameters.
# ---------------------------------------------------------------------------------------------------------------------
/* Required update for different app */
variable "app_name" {
  description = ""
  default     = "nakamabase"
}

variable "key_name" {
  description = ""
  type        = string
  default     = "nakama_backend_dev"
}

variable "ami_name" {
  description = ""
  default     = "amazon-docker-server-nakama-nakamabase"
}

// ======================================================
/* Optional update for different app */
variable "instance_type" {
  description = "The type of EC2 Instances to run (e.g. t2.micro)"
  type        = string
  default     = "t3.micro"
}

variable "cpu_credits_type" {
  description = "The name to use for EC2 instance"
  type        = string
  default     = "standard"
}

variable "ami_owner" {
  description = "Filter for the AMI owner"
  default     = "self"
}

variable "game_service_version" {
  description = ""
  default     = "latest"
}