#!/bin/bash

type="$1"
[ -z "$1" ] && type="patch"

NAME=ami-amz-docker-live-nkmfd

versionFile="./packer/version-${NAME}.txt"
curVersion=$(head -n 1 ${versionFile})
nextVersion=$(./version_inc.sh "${curVersion}" ${type})
echo "${nextVersion}"

echo "${nextVersion}" > ${versionFile}
git status
git add ${versionFile}
git commit -m "ami(${NAME}): Update ami image with Packer to version ${nextVersion}"
git status
nextGitTag=${nextVersion}-${NAME}
git tag -a "${nextGitTag}" -m "Release ami ($NAME) image with Packer to version ${nextGitTag}"
git push origin main
git push origin "${nextGitTag}"
