#!/bin/bash

# --- 配置区 ---
IAM_USER_NAME="prometheus-ec2-sd-user"
IAM_POLICY_NAME="PrometheusEC2ServiceDiscoveryPolicy"
AWS_REGION="us-east-1" # 默认区域，用于后续的Prometheus配置说明

# --- 函数定义 ---

# 检查命令是否存在
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# 打印错误信息并退出
error_exit() {
  echo "Error: $1" >&2
  exit 1
}

# --- 脚本开始 ---

echo "--- 开始为 Prometheus 设置 AWS IAM 权限 ---"

# 检查 AWS CLI 是否安装
if ! command_exists aws; then
  error_exit "AWS CLI 未安装。请先安装 AWS CLI (https://aws.amazon.com/cli/) 并配置好权限。"
fi

# 检查 jq 是否安装 (用于解析 JSON 输出)
if ! command_exists jq; then
  error_exit "jq 未安装。请先安装 jq (https://jqlang.github.io/jq/download/)，它是解析JSON必需的工具。"
fi

echo "正在检查 IAM 用户 '$IAM_USER_NAME'..."

# 检查 IAM 用户是否存在
USER_EXISTS=$(aws iam get-user --user-name "$IAM_USER_NAME" --output text 2>/dev/null | grep -c "$IAM_USER_NAME")

if [ "$USER_EXISTS" -eq 0 ]; then
  echo "用户 '$IAM_USER_NAME' 不存在，正在创建..."
  aws iam create-user --user-name "$IAM_USER_NAME" || error_exit "创建 IAM 用户失败。"
else
  echo "用户 '$IAM_USER_NAME' 已存在。"
fi

echo "正在创建或更新 IAM 策略 '$IAM_POLICY_NAME'..."

# 定义策略 JSON
IAM_POLICY_DOCUMENT='{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ec2:DescribeInstances",
                "ec2:DescribeTags"
            ],
            "Resource": "*"
        }
    ]
}'

# 检查策略是否存在
POLICY_ARN=$(aws iam list-policies --scope Local --query "Policies[?PolicyName=='$IAM_POLICY_NAME'].Arn" --output text 2>/dev/null)

if [ -z "$POLICY_ARN" ]; then
  echo "策略 '$IAM_POLICY_NAME' 不存在，正在创建..."
  POLICY_ARN=$(aws iam create-policy --policy-name "$IAM_POLICY_NAME" --policy-document "$IAM_POLICY_DOCUMENT" --query "Policy.Arn" --output text) || error_exit "创建 IAM 策略失败。"
  echo "策略 ARN: $POLICY_ARN"
else
  echo "策略 '$IAM_POLICY_NAME' 已存在，正在更新策略版本 (如果策略文档不同)..."
  # 获取当前策略的默认版本 ID
  DEFAULT_VERSION_ID=$(aws iam get-policy --policy-arn "$POLICY_ARN" --query "Policy.DefaultVersionId" --output text)
  # 获取策略文档内容，并移除所有空白字符以便比较
  CURRENT_POLICY_DOCUMENT_FLAT=$(aws iam get-policy-version --policy-arn "$POLICY_ARN" --version-id "$DEFAULT_VERSION_ID" --query "PolicyVersion.Document" --output json | jq -c '.' | tr -d '\n\r\t ')
  # 准备新的策略文档进行比较
  NEW_POLICY_DOCUMENT_FLAT=$(echo "$IAM_POLICY_DOCUMENT" | jq -c '.' | tr -d '\n\r\t ')

  if [ "$CURRENT_POLICY_DOCUMENT_FLAT" != "$NEW_POLICY_DOCUMENT_FLAT" ]; then
    echo "检测到策略文档差异，正在创建新策略版本并设置为默认..."
    aws iam create-policy-version --policy-arn "$POLICY_ARN" --policy-document "$IAM_POLICY_DOCUMENT" --set-as-default || error_exit "创建策略新版本失败。"
    echo "策略已更新到新版本。"
  else
    echo "策略文档内容相同，无需更新。"
  fi
fi

echo "正在附加策略 '$IAM_POLICY_NAME' 到用户 '$IAM_USER_NAME'..."

# 检查策略是否已附加
ATTACHED=$(aws iam list-attached-user-policies --user-name "$IAM_USER_NAME" --query "AttachedPolicies[?PolicyName=='$IAM_POLICY_NAME'].PolicyName" --output text 2>/dev/null)

if [ -z "$ATTACHED" ]; then
  aws iam attach-user-policy --user-name "$IAM_USER_NAME" --policy-arn "$POLICY_ARN" || error_exit "附加策略到用户失败。"
  echo "策略已成功附加到用户。"
else
  echo "策略已附加到用户。"
fi

echo "正在生成 Access Key..."

# 检查现有 Access Key
EXISTING_KEYS=$(aws iam list-access-keys --user-name "$IAM_USER_NAME" --query "AccessKeyMetadata[].AccessKeyId" --output text 2>/dev/null)

if [ -n "$EXISTING_KEYS" ]; then
  echo "用户 '$IAM_USER_NAME' 已经拥有 Access Key(s): $EXISTING_KEYS"
  read -p "是否要生成新的 Access Key？(y/N): " CREATE_NEW_KEY
  if [[ "$CREATE_NEW_KEY" != [yY] ]]; then
    echo "未生成新的 Access Key。请使用现有的 Access Key。"
    exit 0
  fi
  # 如果要生成新的，且用户已有两个密钥，建议删除一个旧的。
  # AWS 限制每个用户最多有两个活动密钥。这里不做自动删除，需要手动处理。
  KEY_COUNT=$(echo "$EXISTING_KEYS" | wc -w)
  if [ "$KEY_COUNT" -ge 2 ]; then
    echo "警告：用户已达到最大 Access Key 数量 (2)。请手动删除一个旧密钥再尝试创建新密钥。"
    echo "可以使用 'aws iam delete-access-key --access-key-id <旧密钥ID> --user-name $IAM_USER_NAME' 删除。"
    error_exit "无法创建新的 Access Key。"
  fi
fi

# 创建 Access Key
KEY_INFO=$(aws iam create-access-key --user-name "$IAM_USER_NAME" --output json) || error_exit "创建 Access Key 失败。"

ACCESS_KEY_ID=$(echo "$KEY_INFO" | jq -r '.AccessKey.AccessKeyId')
SECRET_ACCESS_KEY=$(echo "$KEY_INFO" | jq -r '.AccessKey.SecretAccessKey')

if [ -z "$ACCESS_KEY_ID" ] || [ -z "$SECRET_ACCESS_KEY" ]; then
  error_exit "未能获取 Access Key ID 或 Secret Access Key。"
fi

echo "--- 凭证生成成功！---"
echo "Access Key ID:     $ACCESS_KEY_ID"
echo "Secret Access Key: $SECRET_ACCESS_KEY"
echo "请务必妥善保管 Secret Access Key，它只显示一次。"

echo ""
echo "--- 配置你的 Prometheus ---"
echo "你现在可以将这些凭证添加到你的 Prometheus 配置中。有以下两种推荐方法："
echo ""

echo "1. **通过环境变量** (适用于 1Panel Docker 部署 Prometheus):"
echo "   在 1Panel 的 Prometheus 容器配置中，添加以下环境变量："
echo "   - AWS_ACCESS_KEY_ID=$ACCESS_KEY_ID"
echo "   - AWS_SECRET_ACCESS_KEY=$SECRET_ACCESS_KEY"
echo "   - AWS_REGION=$AWS_REGION"
echo ""

echo "2. **通过共享凭证文件** (适用于宿主机或挂载到 Docker 容器):"
echo "   在你的 Prometheus 宿主机或挂载到 Docker 容器的路径下，创建或编辑 AWS 共享凭证文件 (例如 ~/.aws/credentials)。"
echo "   如果文件不存在，请先创建它并确保权限为 600 (chmod 600 ~/.aws/credentials)。"
echo "   建议使用一个专门的 profile，例如 '[prometheus]'，而不是默认的 '[default]'。"
echo ""
echo "   --- 文件内容示例 ---"
echo "   [prometheus]"
echo "   aws_access_key_id = $ACCESS_KEY_ID"
echo "   aws_secret_access_key = $SECRET_ACCESS_KEY"
echo "   ----------------------"
echo ""
echo "   然后在 Prometheus 的 prometheus.yml 中，ec2_sd_configs 部分添加 'profile: prometheus'："
echo "     ec2_sd_configs:"
echo "       - region: $AWS_REGION"
echo "         profile: prometheus # <--- 添加这一行"
echo "         port: 9100"
echo "         ..."
echo ""
echo "--- 完成 ---"