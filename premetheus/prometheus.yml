global:
  scrape_interval: 15s # Set the scrape interval to every 15 seconds. Default is every 1 minute.
  evaluation_interval: 15s # Evaluate rules every 15 seconds. The default is every 1 minute.
  # scrape_timeout is set to the global default (10s).

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
# - "first_rules.yml"
# - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
# Here it's Prometheus itself.
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  # - job_name: 'prometheus'
  #   static_configs:
  #     - targets: [ 'localhost:9090' ]
  - job_name: 'nkmad_ec2_realtime'
    static_configs:
      - targets: ['realtime.nkmad.pwglab.com:9100'] # 使用私有IP或DNS
        labels:
          instance_name: 'nkmad_realtime_ec2' # 添加自定义标签方便识别
          environment: 'Prod'
          app: 'nkmad'
          type: 'ec2'
  - job_name: 'nkmad_nakama_realtime'
    static_configs:
      - targets: ['realtime.nkmad.pwglab.com:9101'] # 使用私有IP或DNS
        labels:
          instance_name: 'nkmad_realtime_ec2' # 添加自定义标签方便识别
          environment: 'Prod'
          app: 'nkmad'
          type: 'nakama'
  - job_name: 'nkmad_ec2_auto_scaling_nodes'
    ec2_sd_configs:
      - region: us-east-1 # 替换为你的 AWS 区域
        port: 9100 # Node Exporter 端口
        # 过滤条件 (可选，但强烈推荐，以避免抓取所有 EC2 实例)
        filters:
          - name: instance-state-name
            values: ['running']
          # - name: tag:aws:autoscaling:groupName # 假设你的 ASG 实例有这个标签
          #   values: ['nkmad-nakama-db-rpc-asg-prod'] # 替换为你的 Auto Scaling 组名称
          - name: tag:Name # 假设你的 ASG 实例有这个标签
            values: ['nkmad-nakama-db-rpc-prod'] # 替换为你的 Auto Scaling 组名称
          # 或者你可以根据其他标签过滤，例如：
          - name: tag:App
            values: ['nkmad']
    # Relabeling (可选，用于优化标签)
    relabel_configs:
      # 重点简化: 直接将 __address__ 替换为公有IP和固定的端口
      - source_labels: [ __meta_ec2_public_ip ] # 源标签只需要公有IP
        target_label: __address__
        replacement: '$1:9100' # 直接拼接公有IP和固定端口
        action: replace
      # 示例1: 添加一个固定的标签
      - source_labels: [ __address__ ] # 任意一个源标签，只要存在即可触发relabeling
        target_label: app
        replacement: 'nkmad'
      - source_labels: [ __address__ ] # 任意一个源标签，只要存在即可触发relabeling
        target_label: type
        replacement: 'ec2'
      - source_labels: [ __address__ ] # 任意一个源标签，只要存在即可触发relabeling
        target_label: environment
        replacement: 'prod'
      # 将 EC2 实例的 private IP 作为 instance 标签
      - source_labels: [__meta_ec2_private_ip]
        target_label: private_ip
      - source_labels: [__meta_ec2_public_ip]
        target_label: public_ip
      # 将 EC2 实例的 instance-id 作为新的标签
      - source_labels: [__meta_ec2_instance_id]
        target_label: instance_id
      # AWS 的 EC2 标签会在服务发现时以 __meta_ec2_tag_<tagname> 的形式暴露
      # 这里我们将 __meta_ec2_tag_App 转换为 app 标签
      - source_labels: [ __meta_ec2_tag_App ]
        regex: '(.*)' # 匹配所有内容
        target_label: app
        replacement: '$1' # 使用匹配到的内容作为标签值
        action: replace
      - source_labels: [__meta_ec2_tag_Name]
        regex: '(.*)'
        target_label: instance_name
        replacement: '$1'
        action: replace

  - job_name: 'nkmad_nakama_auto_scaling_nodes'
    ec2_sd_configs:
      - region: us-east-1 # 替换为你的 AWS 区域
        port: 9101 # Nakama 端口
        # 过滤条件 (可选，但强烈推荐，以避免抓取所有 EC2 实例)
        filters:
          - name: instance-state-name
            values: ['running']
          # - name: tag:aws:autoscaling:groupName # 假设你的 ASG 实例有这个标签
          #   values: ['nkmad-nakama-db-rpc-asg-prod'] # 替换为你的 Auto Scaling 组名称
          - name: tag:Name # 假设你的 ASG 实例有这个标签
            values: ['nkmad-nakama-db-rpc-prod'] # 替换为你的 Auto Scaling 组名称
          # 或者你可以根据其他标签过滤，例如：
          - name: tag:App
            values: ['nkmad']
    # Relabeling (可选，用于优化标签)
    relabel_configs:
      # 重点简化: 直接将 __address__ 替换为公有IP和固定的端口
      - source_labels: [ __meta_ec2_public_ip ] # 源标签只需要公有IP
        target_label: __address__
        replacement: '$1:9101' # 直接拼接公有IP和固定端口
        action: replace
      # 示例1: 添加一个固定的标签
      - source_labels: [ __address__ ] # 任意一个源标签，只要存在即可触发relabeling
        target_label: app
        replacement: 'nkmad'
      - source_labels: [ __address__ ] # 任意一个源标签，只要存在即可触发relabeling
        target_label: type
        replacement: 'nakama'
      - source_labels: [ __address__ ] # 任意一个源标签，只要存在即可触发relabeling
        target_label: environment
        replacement: 'prod'
      # 将 EC2 实例的 private IP 作为 instance 标签
      - source_labels: [__meta_ec2_private_ip]
        target_label: private_ip
      - source_labels: [__meta_ec2_public_ip]
        target_label: public_ip
      # 将 EC2 实例的 instance-id 作为新的标签
      - source_labels: [__meta_ec2_instance_id]
        target_label: instance_id
      # AWS 的 EC2 标签会在服务发现时以 __meta_ec2_tag_<tagname> 的形式暴露
      # 这里我们将 __meta_ec2_tag_App 转换为 app 标签
      - source_labels: [ __meta_ec2_tag_App ]
        regex: '(.*)' # 匹配所有内容
        target_label: app
        replacement: '$1' # 使用匹配到的内容作为标签值
        action: replace
      - source_labels: [__meta_ec2_tag_Name]
        regex: '(.*)'
        target_label: instance_name
        replacement: '$1'
        action: replace
