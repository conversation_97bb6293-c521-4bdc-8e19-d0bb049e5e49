AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: An AWS Lambda application that calls the Lambda API.
Resources:
  function:
    Type: AWS::Serverless::Function
    Properties:
      Handler: index.handler
      Runtime: nodejs20.x
      CodeUri: s3://lambda-artifacts-1b52ef3fd3581b84/096cc9aa5615439c963a807b9a5fce56
      Description: Call the AWS Lambda API
      Timeout: 600
      Policies:
      - AWSLambdaBasicExecutionRole
      - AWSLambda_ReadOnlyAccess
      - AWSXrayWriteOnlyAccess
      - Statement:
        - Effect: Allow
          Action:
          - ssm:GetParameter
          - route53:ListHostedZonesByName
          - route53:ListHostedZones
          - route53:ChangeResourceRecordSets
          - route53:GetChange
          - route53:ListResourceRecordSets
          Resource: '*'
      Tracing: Active
      Layers:
      - Ref: libs
      Environment:
        Variables:
          NAKAMA_BACKEND_TAG: 1.0.50
          NAKAMA_PROD_NODE_ID: '0'
          IP_ODD: *************
          IP_EVEN: *************
          UNIQUE_PROD_NODE_0_DOMAIN: prod-node-0.nkmfd.pwglab.com
  libs:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: nkmfd-node-switcher-lib
      Description: Dependencies for the blank sample app.
      ContentUri: s3://lambda-artifacts-1b52ef3fd3581b84/ef94a7b806c36ced9d1ab3e2c2d895cc
      CompatibleRuntimes:
      - nodejs20.x
  AllowCloudWatchInvokePermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: function
      Action: lambda:InvokeFunction
      Principal: lambda.alarms.cloudwatch.amazonaws.com
      SourceAccount: ************
      SourceArn: arn:aws:cloudwatch:us-east-1:************:alarm:nkmfd_rds_write_iops_less_10
