AWSTemplateFormatVersion: '2010-09-09'
Transform: 'AWS::Serverless-2016-10-31'
Description: An AWS Lambda application that calls the Lambda API.
Resources:
  function:
    Type: AWS::Serverless::Function
    Properties:
      Handler: index.handler
      Runtime: nodejs20.x
      CodeUri: function/.
      Description: Call the AWS Lambda API
      Timeout: 600
      # Function's execution role
      Policies:
        - AWSLambdaBasicExecutionRole
        - AWSLambda_ReadOnlyAccess
        - AWSXrayWriteOnlyAccess
        # 添加对 S3、SSM 和 Route53 的权限
        - Statement:
            - Effect: Allow
              Action:
#                - s3:GetObject
                - ssm:GetParameter
                - route53:ListHostedZonesByName
                - route53:ListHostedZones
                - route53:ChangeResourceRecordSets
                - route53:GetChange
                - route53:ListResourceRecordSets
              Resource: "*"
      Tracing: Active
      Layers:
        - !Ref libs
      Environment:
        Variables:
          NAKAMA_BACKEND_TAG: "1.0.50"
          NAKAMA_PROD_NODE_ID: "0"
          IP_ODD: "*************"
          IP_EVEN: "*************"
          UNIQUE_PROD_NODE_0_DOMAIN: "prod-node-0.nkmfd.pwglab.com"
  libs:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: nkmfd-node-switcher-lib
      Description: Dependencies for the blank sample app.
      ContentUri: lib/.
      CompatibleRuntimes:
        - nodejs20.x
  AllowCloudWatchInvokePermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref function
      Action: lambda:InvokeFunction
      Principal: lambda.alarms.cloudwatch.amazonaws.com
      SourceAccount: ************
      SourceArn: arn:aws:cloudwatch:us-east-1:************:alarm:nkmfd_rds_write_iops_less_10