const index = require('./index')
const fs = require('fs')
const AWSXRay = require('aws-xray-sdk-core')
const {
  nkmfdNodeSwitcher,
  checkVersion,
  runSshCommand,
  getParameter,
  switchUniqueDomainIp,
  getTargetIp,
  getRecordIP,
  updateRoute53Record,
  UNIQUE_PROD_NODE_0_DOMAIN,
  IpEven,
  IpOdd,
  handler
} = require('./index')
AWSXRay.setContextMissingStrategy('LOG_ERROR')
// import { beforeEach, describe, expect, jest, test } from '@jest/globals'
// import { expect } from '@jest/expect'

test('Runs function handler', async () => {
    let eventFile = fs.readFileSync('event.json')
    let event = JSON.parse(eventFile)
    let response = await handler(event, null)
    expect(JSON.stringify(response)).toContain('AccountLimit')
  }
)
jest.setTimeout(60000);  // 设置超时时间为 30 秒
test('Runs nkmfdNodeSwitcher', async () => {

    jest.setTimeout(30000)  // 设置超时时间为 30 秒
    await nkmfdNodeSwitcher()
  }
)


test('Runs getRecordIP', async () => {
    let result = await getRecordIP(UNIQUE_PROD_NODE_0_DOMAIN, 'pwglab.com')
    console.log(result)
  }
)

test('Runs updateRoute53Record', async () => {
    let result = await updateRoute53Record(UNIQUE_PROD_NODE_0_DOMAIN, IpOdd, 'pwglab.com')
    console.log(result)
  }
)

test('Runs getTargetIp', async () => {
    let result = await getTargetIp(IpOdd)
    console.log(result)
    expect(result).toBe(IpEven)
  }
)

test('Runs logic', async () => {
    let currentIp = await getRecordIP(UNIQUE_PROD_NODE_0_DOMAIN, 'pwglab.com')
    let targetIp = getTargetIp(currentIp)
    await updateRoute53Record(UNIQUE_PROD_NODE_0_DOMAIN, targetIp)
    let afterUpdateIp = await getRecordIP(UNIQUE_PROD_NODE_0_DOMAIN, 'pwglab.com')
    console.log(`ip switch from ${currentIp} to ${targetIp}`)
    expect(afterUpdateIp).toBe(targetIp)
  }
)
test('Runs switchUniqueDomainIp', async () => {
    let currentIp = await getRecordIP(UNIQUE_PROD_NODE_0_DOMAIN, 'pwglab.com')
    let targetIp = getTargetIp(currentIp)

    let afterUpdateIp = await switchUniqueDomainIp()
    console.log(`ip switch from ${currentIp} to ${targetIp} and real finish afterUpdateIp :${afterUpdateIp}`)
    expect(afterUpdateIp).toBe(targetIp)
  }
)

test('Runs getParameter', async () => {
    let a = await getParameter('/nkmfd/prod-node-0/http-key')
    let b = await getParameter('/nkmfd/prod-node-0/ssh-private-key')
    console.log(a)
    console.log(b)
  }
)

test('Runs runSshCommand1', async () => {
    await runSshCommand(IpOdd, 'docker ps')
  }
)

test('Runs runSshCommand2', async () => {
    await runSshCommand(IpEven, 'docker ps')
  }
)

test('Runs checkVersion', async () => {
    const version = await checkVersion(IpOdd)
    console.log(version)
  }
)
