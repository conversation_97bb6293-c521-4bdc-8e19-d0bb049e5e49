const AWSXRay = require('aws-xray-sdk-core')
const { LambdaClient, GetAccountSettingsCommand } = require('@aws-sdk/client-lambda')
const { SSMClient, GetParameterCommand } = require('@aws-sdk/client-ssm')
const {
  Route53Client,
  ListHostedZonesByNameCommand,
  ListResourceRecordSetsCommand,
  ChangeResourceRecordSetsCommand
} = require('@aws-sdk/client-route-53')
const http = require('http')
const { execSync } = require('child_process')

// 固定 IP 定义
const IpOdd = process.env.IP_ODD || '*************' // odd
const IpEven = process.env.IP_EVEN || '*************' // even // TODO: 替换成 Three, 当前 live 的 instance

// Lambda 环境变量（可在 Lambda 配置中设置）
const NAKAMA_BACKEND_TAG = process.env.NAKAMA_BACKEND_TAG || '1.0.50'
const NAKAMA_PROD_NODE_ID = process.env.NAKAMA_PROD_NODE_ID || '0'

const UNIQUE_PROD_NODE_0_DOMAIN = process.env.UNIQUE_PROD_NODE_0_DOMAIN || 'prod-node-0.nkmfd.pwglab.com' // TODO: 替换成真正的 instance

const region = 'us-east-1'
// 初始化 Route53 客户端
const route53Client = new Route53Client({ region: region })

const client = new SSMClient({ region: region })
const { Client } = require('ssh2')

async function getRecordIP(domainName, rootDomain = 'pwglab.com') {
  // 获取 Route 53 的记录 IP 地址
  const listZonesCommand = new ListHostedZonesByNameCommand({ DNSName: rootDomain })
  const hostedZones = await route53Client.send(listZonesCommand)

  if (!hostedZones.HostedZones || hostedZones.HostedZones.length === 0) {
    throw new Error(`Hosted Zone not found for domain name: ${domainName}`)
  }

  const hostedZoneId = hostedZones.HostedZones[0].Id.split('/').pop()
  const listRecordsCommand = new ListResourceRecordSetsCommand({ HostedZoneId: hostedZoneId })
  const recordSets = await route53Client.send(listRecordsCommand)

  const record = recordSets.ResourceRecordSets.find(
    (r) => r.Name.replace(/\.$/, '') === domainName
  )
  if (!record) {
    throw new Error(`Record not found for domain ${domainName}`)
  }

  return record.ResourceRecords[0].Value
}

const ipMap = {
  [IpOdd]: IpEven, // odd -> even
  [IpEven]: IpOdd // even -> odd
}

function getTargetIp(currentIp) {
  if (!(currentIp in ipMap)) {
    throw new Error('Current IP does not match the predefined IPs.')
  }
  return ipMap[currentIp]
}

async function updateRoute53Record(domainName, newIP, rootDomain = 'pwglab.com') {
  // 更新 Route 53 的记录到新 IP
  const listZonesCommand = new ListHostedZonesByNameCommand({ DNSName: rootDomain })
  const hostedZones = await route53Client.send(listZonesCommand)

  if (!hostedZones.HostedZones || hostedZones.HostedZones.length === 0) {
    throw new Error(`Hosted Zone not found for domain name: ${domainName}`)
  }

  const hostedZoneId = hostedZones.HostedZones[0].Id.split('/').pop()

  const changeBatch = {
    HostedZoneId: hostedZoneId,
    ChangeBatch: {
      Comment: 'Update IP',
      Changes: [
        {
          Action: 'UPSERT',
          ResourceRecordSet: {
            Name: domainName,
            Type: 'A',
            TTL: 300,
            ResourceRecords: [{ Value: newIP }]
          }
        }
      ]
    }
  }

  const changeCommand = new ChangeResourceRecordSetsCommand(changeBatch)
  const response = await route53Client.send(changeCommand)
  return response
}

/**
 * 该方法:
 * 1. 获取当前的IP
 * 2. 判断需要切换的IP
 * 3. 切换 UNIQUE_PROD_NODE_0_DOMAIN 的 IP
 *
 * @return {Promise<*>}
 */
async function switchUniqueDomainIp() {
  let currentIp = await getRecordIP(UNIQUE_PROD_NODE_0_DOMAIN, 'pwglab.com')
  let targetIp = getTargetIp(currentIp)
  await updateRoute53Record(UNIQUE_PROD_NODE_0_DOMAIN, targetIp)
  return await getRecordIP(UNIQUE_PROD_NODE_0_DOMAIN, 'pwglab.com')
}

/**
 * 从 Parameter store 中获取 parameter
 * @param parameterName
 * @return {Promise<*>}
 */
async function getParameter(parameterName) {
  // 创建一个 GetParameterCommand 请求对象
  const command = new GetParameterCommand({
    Name: parameterName,
    WithDecryption: true  // 如果参数类型为 SecureString，设置为 true 来解密
  })

  try {
    // 执行命令并等待结果
    const data = await client.send(command)
    // console.log(`Parameter Value: ${data.Parameter.Value}`)
    return data.Parameter.Value
  } catch (error) {
    console.error('Error fetching parameter:', error)
  }
}

/**
 *
 * 该方法在指定的 nkmfd 的 prod node ip instance 中运行指定 command
 *
 * @param ip
 * @param command
 * @param username
 * @return {Promise<unknown>}
 */
async function runSshCommand(ip, command, username = 'ec2-user') {
  const privateKey = await getParameter('/nkmfd/prod-node-0/ssh-private-key')
  const ssh = new Client()

  return new Promise((resolve, reject) => {
    ssh.on('ready', () => {
      console.log('SSH Connection established.')
      ssh.exec(command, (err, stream) => {
        if (err) {
          reject(`SSH command error: ${err.message}`)
        }

        stream.on('close', (code, signal) => {
          console.log('Stream closed with code:', code)
          ssh.end()
          resolve({ status: 'success', message: 'Command executed successfully.' })
        }).on('data', (data) => {
          console.log('STDOUT:', data.toString())
        }).on('stderr', (data) => {
          console.error('STDERR:', data.toString())
        })
      })
    }).on('error', (err) => {
      console.error('SSH connection error:', err)
      reject(`SSH connection error: ${err.message}`)
    }).connect({
      host: ip,
      port: 22,
      username: username,
      privateKey: privateKey
    })
  })
}

// function runRemoteCommands(ip) {
//   // 执行远程命令
//   const commands = `
//     ssh ${ip} <<EOF
//     docker compose down
//     export NAKAMA_BACKEND_TAG=${NAKAMA_BACKEND_TAG} && export NAKAMA_PROD_NODE_ID=${NAKAMA_PROD_NODE_ID} && docker compose up -d
//     EOF
//   `
//   execSync(commands, { stdio: 'inherit' })
// }

async function checkVersion(ip) {
  const httpKey = await getParameter('/nkmfd/prod-node-0/http-key')
  // 通过 HTTP 请求验证 NAKAMA 版本
  return new Promise((resolve, reject) => {
    const options = {
      hostname: ip,
      port: 7350,
      path: `/v2/rpc/s2s_get_version?http_key=${httpKey}&unwrap=true`,
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'Content-Type': 'application/json'
      }
    }

    const req = http.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => {
        data += chunk
      })
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data)
          resolve(jsonData.version)
        } catch (error) {
          reject(error)
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.write(JSON.stringify({}))
    req.end()
  })
}

/**
 *
 *   // 首先检查当前的 ip 地址, currentIp
 *   // 然后获取需要更新的 instance 的 ip 地址 targetIp
 *   // 针对 targetIp 执行 docker 启动最新的 docker compose
 *   // 等待 10s
 *   // 开始检查 targetIp 的版本信息
 *   // 打印信息并判断是否匹配配置
 *   // 切换 IP
 *
 * @return {Promise<void>}
 */
async function nkmfdNodeSwitcher() {
  const currentIp = await getRecordIP(UNIQUE_PROD_NODE_0_DOMAIN, 'pwglab.com')
  console.log(`✅currentIp ${UNIQUE_PROD_NODE_0_DOMAIN} currentIp is ${currentIp}`)
  const targetIp = getTargetIp(currentIp)
  console.log(`✅targetIp ${UNIQUE_PROD_NODE_0_DOMAIN} targetIp is ${targetIp}`)
  console.log(`NAKAMA_BACKEND_TAG: ${NAKAMA_BACKEND_TAG}`)
  console.log(`NAKAMA_PROD_NODE_ID: ${NAKAMA_PROD_NODE_ID}`)
  let sshCommand = `docker compose down && export NAKAMA_BACKEND_TAG=${NAKAMA_BACKEND_TAG} && export NAKAMA_PROD_NODE_ID=${NAKAMA_PROD_NODE_ID} && docker compose up -d`

  console.log(`✅start to run on ip ${targetIp} with command [${sshCommand}]`)
  await runSshCommand(targetIp, sshCommand)
  console.log(`✅finish to run on ip ${targetIp} with command [${sshCommand}], then hold seconds`)
// 等待 10 秒
  await new Promise(resolve => setTimeout(resolve, 10000))
  console.log(`✅start to check version after ssh finish`)
  const targetIpServiceVersion = await checkVersion(targetIp)
  console.log(`✅ ${targetIp} targetIpServiceVersion: ${targetIpServiceVersion}`)
  if (targetIpServiceVersion !== NAKAMA_BACKEND_TAG) {
    console.log(`❌Version mismatch: expected ${NAKAMA_BACKEND_TAG}, got ${targetIpServiceVersion}.`)
  } else {
    console.log(`✅Version matched: expected ${NAKAMA_BACKEND_TAG}, got ${targetIpServiceVersion}.`)
  }
  console.log(`✅start to update ${UNIQUE_PROD_NODE_0_DOMAIN} to ip ${targetIp}`)
  await updateRoute53Record(UNIQUE_PROD_NODE_0_DOMAIN, targetIp)
}

// Create client outside of handler to reuse
const lambda = AWSXRay.captureAWSv3Client(new LambdaClient())

// Handler
exports.handler = async function(event, context) {
  // event.Records.forEach(record => {
  //   console.log(record.body)
  // })
  console.log(event)
  // console.log('## ENVIRONMENT VARIABLES: ' + serialize(process.env))
  // console.log('## CONTEXT: ' + serialize(context))
  // console.log('## EVENT: ' + serialize(event))

  await nkmfdNodeSwitcher()
  return getAccountSettings()
}

// Use SDK client
var getAccountSettings = function() {
  return lambda.send(new GetAccountSettingsCommand())
}

var serialize = function(object) {
  return JSON.stringify(object, null, 2)
}

if (process.env.NODE_ENV === 'test') {
// 导出方法用于测试
  module.exports = {
    nkmfdNodeSwitcher,
    checkVersion,
    runSshCommand,
    getParameter,
    switchUniqueDomainIp,
    getTargetIp,
    getRecordIP,
    updateRoute53Record,
    IpOdd,
    IpEven,
    UNIQUE_PROD_NODE_0_DOMAIN
  }
}