#!/bin/bash
version="$1"
major=0
minor=0
patch=0

# break down the version number into it's components
regex="([0-9]+).([0-9]+).([0-9]+)"
if [[ $version =~ $regex ]]; then
  major="${BASH_REMATCH[1]}"
  minor="${BASH_REMATCH[2]}"
  patch="${BASH_REMATCH[3]}"
fi

# check parameter to see which number to increment
if [[ "$2" == "major" ]]; then
  major=$(echo "$major"+1 | bc)
elif [[ "$2" == "minor" ]]; then
  minor=$(echo "$minor" + 1 | bc)
elif [[ "$2" == "patch" ]]; then
  patch=$(echo "$patch" + 1 | bc)
else
  echo "usage: ./version_inc.sh version_number [major/minor/patch]"
  exit 0
fi
newVersion=${major}.${minor}.${patch}
# echo the new version number
#echo "new version: ${newVersion}"
echo "${newVersion}"
