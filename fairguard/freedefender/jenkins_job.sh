echo "==== start to FairGuard the aab file"
echo "==== check current aab"
ls target/

NAME=FreeDefender
echo "==== 将原始 abb 移动到 origin 文件夹, 并且统一文件名为 Name"
mkdir target/origin
mv target/*.aab target/origin/${NAME}.aab
echo "==== 开始执行 FairGuard 加固, 并将文件生成到 target/origin 根目录"
java -jar  /Volumes/Data/tools/fairguard-5.2.5/FairGuard5.2.5.jar -autoconfig -inputfile target/origin/${NAME}.aab -outputfile target/origin/${NAME}-fairguard.aab
ls target/origin
ls target/

echo "==== 开始执行 FairGuard 签名, 并将文件生成到 target 根目录"
java -jar  /Volumes/Data/tools/fairguard-5.2.5/FairGuard5.2.5.jar -optype_sign_jar -inputfile target/origin/${NAME}-fairguard.aab -outputfile target/${NAME}-fairguard-sign.aab
ls target/origin
ls target/
echo "==== 我们最终需要上传 target/*.aab"