version: '3'
services:
  nakama:
    image: ghcr.io/playwindgames/nkmad/nakama-app:${NAKAMA_BACKEND_TAG}
    # 新增：启用 host 网络模式
    network_mode: "host"
    entrypoint:
      - "/bin/sh"
      - "-ecx"
      - >
        /nakama/nakama migrate up --database.address ${DB_ADDRESS} &&
        exec /nakama/nakama --config /nakama/data/local.yml --name ad-${NAKAMA_PROD_NODE_ID} --database.address ${DB_ADDRESS} --logger.level INFO --metrics.prometheus_port 9101
    restart: "no"
#    depends_on:
#      prometheus:
#        condition: service_started
    logging:
      driver: syslog
      options:
        tag: "nkmad-${NAKAMA_PROD_NODE_ID}"
    # 移除：在 host 模式下，ports 和 expose 映射是多余且无效的。
    # 容器内的服务将直接监听宿主机的端口。
    # 根据您之前的设置，服务现在会直接占用宿主机的 7349, 7350, 7351, 7335, 和 9100 端口。
    # expose:
    #   - "7349"
    #   - "7350"
    #   - "7351"
    #   - "7335" # cluster peer port
    #   - "9100"
    # ports:
    #   - "7349:7349"
    #   - "7350:7350"
    #   - "7351:7351"
    #   - "7335:7335" # cluster peer port
    #   - "9101:9100"
    healthcheck:
      # healthcheck 仍然有效，因为它在容器内执行，localhost 会指向宿主机网络
      test: [ "CMD", "curl", "-f", "http://localhost:7350/" ]
      interval: 10s
      timeout: 5s
      retries: 5
volumes:
  data: