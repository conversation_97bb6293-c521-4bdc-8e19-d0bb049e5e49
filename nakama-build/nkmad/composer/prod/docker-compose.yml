version: '3'
services:
  nakama:
    image: ghcr.io/playwindgames/nkmad/nakama-app:${NAKAMA_BACKEND_TAG}
    entrypoint:
      - "/bin/sh"
      - "-ecx"
      - >
        /nakama/nakama migrate up --database.address ${DB_ADDRESS} &&
        exec /nakama/nakama --config /nakama/data/local.yml --name nkmad-${NAKAMA_PROD_NODE_ID} --database.address ${DB_ADDRESS} --logger.level INFO --metrics.prometheus_port 9100
    restart: "no"
#    depends_on:
#      prometheus:
#        condition: service_started
    logging:
      driver: syslog
      options:
        tag: "nkmad-${NAKAMA_PROD_NODE_ID}"
    expose:
      - "7349"
      - "7350"
      - "7351"
      - "9100"
    ports:
      - "7349:7349"
      - "7350:7350"
      - "7351:7351"
      - "9101:9100"
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:7350/" ]
      interval: 10s
      timeout: 5s
      retries: 5
volumes:
  data:
