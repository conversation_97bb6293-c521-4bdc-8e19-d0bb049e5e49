# https://heroiclabs.com/docs/nakama/getting-started/configuration/index.html
# name [node in ctx] is override by runtime command in docker-compose file
name: nkmfd-1
data_dir: './data/'

logger:
  stdout: true
  level: 'DEBUG'
  file: '/nakama/data/logfile.log'
  max_age: 30 # 30 days
  max_backups: 5 # 3 month
  rotation: true
  max_size: 100

# NK_XXX are replaced by GitHub Secret dynamically during the build process
console:
  port: 7351
  max_message_size_bytes: 409600
  username: "FcmlchoBfk"
  password: "YyMaiPQJf4j_0x12d"

runtime:
  js_entrypoint: 'build/index.js'
  js_max_count: 128
  http_key: GXr4WXWLy53GKYPLP4j19E
  js_read_only_globals: false
socket:
  max_message_size_bytes: 4096
  max_request_size_bytes: 131072
matchmaker:
  interval_sec: 3

# https://heroiclabs.com/docs/nakama/getting-started/configuration/index.html#session
session:
  token_expiry_sec: 3600
  refresh_token_expiry_sec: 7200

#  For Apple Login Required otherwise get error
#  https://heroiclabs.com/docs/nakama/getting-started/configuration/index.html#apple-1
social:
  apple:
    bundle_id: com.playwindgames.freedefender
iap:
  apple:
    shared_password: '50da7daba22d45a8927f2811a9848686'
  google:
    client_email: '<EMAIL>'
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

google_auth:
  credentials_json: '{"web":*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}'
