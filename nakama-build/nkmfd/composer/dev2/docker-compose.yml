version: '3'
services:
  cockroachdb:
    image: cockroachdb/cockroach:latest-v23.1
    command: start-single-node --insecure --store=attrs=ssd,path=/var/lib/cockroach/
    restart: "no"
    volumes:
      - data:/var/lib/cockroach
    expose:
      - "8080"
      - "26257"
    ports:
      - "26257:26257"
      - "8080:8080"
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080/health?ready=1" ]
      interval: 3s
      timeout: 3s
      retries: 5
  # ================================================================
  # we will use the cloud of cockroachdb cluster
  # nakama --database.address "[USERNAME]:[PWD]@[LINK].cockroachlabs.cloud:26257/defaultdb?sslmode=verify-full"
  # ================================================================
  nakama:
    image: ghcr.io/playwindgames/nkmfd/nakama-app:${NAKAMA_BACKEND_TAG}
    entrypoint:
      - "/bin/sh"
      - "-ecx"
      - >
        /nakama/nakama migrate up --database.address root@cockroachdb:26257 &&
        exec /nakama/nakama --config /nakama/data/dev-config.yml --name nkmfd-0 --database.address root@cockroachdb:26257 --logger.level DEBUG --session.token_expiry_sec 3600 --metrics.prometheus_port 9100
    restart: "no"
    links:
      - 'cockroachdb:db'
    depends_on:
      cockroachdb:
        condition: service_healthy
      prometheus:
        condition: service_started
    volumes:
      - ./data:/nakama/data
    expose:
      - "7349"
      - "7350"
      - "7351"
      - "9100"
    ports:
      - "7349:7349"
      - "7350:7350"
      - "7351:7351"
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:7350/" ]
      interval: 10s
      timeout: 5s
      retries: 5
  prometheus:
    image: prom/prometheus
    entrypoint: /bin/sh -c
    command: |
      'sh -s <<EOF
        cat > ./prometheus.yml <<EON
      global:
        scrape_interval:     15s
        evaluation_interval: 15s

      scrape_configs:
        - job_name: prometheus
          static_configs:
          - targets: ['localhost:9090']

        - job_name: nakama
          metrics_path: /
          static_configs:
          - targets: ['nakama:9100']
      EON
      prometheus --config.file=./prometheus.yml
      EOF'
    ports:
      - '9090:9090'
volumes:
  data:
