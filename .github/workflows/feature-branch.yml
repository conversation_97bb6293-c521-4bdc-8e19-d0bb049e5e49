---
name: feature-branch
on:
  pull_request:
    branches:
      - main
      - release/**
    types: [opened, synchronize, reopened, labeled, unlabeled]

permissions:
  pull-requests: write
  id-token: write
  contents: write

jobs:
  terraform-module:
    uses: cloudposse/github-actions-workflows-terraform-module/.github/workflows/feature-branch.yml@main
    secrets:
      github_access_token: ${{ secrets.REPO_ACCESS_TOKEN }}
