name: Manual Test SSH

on:
  workflow_dispatch:
    inputs:
      test_script_path:
        description: '要执行的测试脚本路径 (例如: /home/<USER>/my_test_script.sh)'
        required: true
        default: '/opt/scripts/run_tests.sh' # 提供一个默认值

jobs:
  run_remote_tests:
    runs-on: ubuntu-latest
    steps:
      - name: Test SSH to Nkmad Realtime
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.NKMAD_REALTIME_SSH_HOST }}
          username: ${{ secrets.NKMAD_REALTIME_SSH_USERNAME }}
          port: ${{ secrets.NKMAD_REALTIME_SSH_PORT }}
          key: ${{ secrets.NKMAD_REALTIME_SSH_PROD_KEY }}
          script: |
            echo "Connecting to remote server and executing test script..."
            # 确保脚本有执行权限
            # chmod +x ${{ github.event.inputs.test_script_path }}
            # 执行用户输入的脚本
            # ${{ github.event.inputs.test_script_path }}
            ls -halt
            pwd
            docker compose ps
            # 你可以在这里添加其他命令，例如：
            # echo "Test script execution complete. Checking logs..."
            # cat /var/log/my_app_tests.log