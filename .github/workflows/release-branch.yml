---
name: release-branch
on:
  push:
    branches:
      - main
      - release/**
    paths-ignore:
      - '.github/**'
      - 'docs/**'
      - 'examples/**'
      - 'test/**'
      - 'README.*'

permissions:
  contents: write
  id-token: write

jobs:
  terraform-module:
    uses: cloudposse/github-actions-workflows-terraform-module/.github/workflows/release-branch.yml@main
    secrets:
      github_access_token: ${{ secrets.REPO_ACCESS_TOKEN }}
