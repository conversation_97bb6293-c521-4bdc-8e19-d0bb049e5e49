name: <PERSON><PERSON><PERSON> Packer nkmfd Dev Amazon Docker Server AMI
on:
#  push:
#    # Sequence of patterns matched against refs/heads
#    branches:
#      # Push events on master branch
#      # - packer
#      # - release
#      # - master
#      # - 0.*.*
#    tags:
#    # - 0.*.*.*-ami          # Push to release tag
#    paths:
#      - 'packer/java-server.json'
#      - 'packer/example.sh'
#      - 'packer/welcome.txt'
  push:
    # Sequence of patterns matched against refs/heads
    tags:
      - 0.*.*-ami-amz-docker-dev-nkmfd          # Push to release tag
# https://hub.docker.com/r/hashicorp/packer/
env:
  # match the github-secret.sh
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_DEVOPS }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEVOPS }}
  AWS_DEFAULT_REGION: us-east-1
  REGION: us-east-1
  INSTALL_REDIS: NO
  NAME: ami-amz-docker-dev-nkmfd
  COCKROACH_DB_CLOUD_DEV_USER: ${{ secrets.COCKROACH_DB_CLOUD_DEV_USER_NKMFD }}
  COCKROACH_DB_CLOUD_DEV_PWD: ${{ secrets.COCKROACH_DB_CLOUD_DEV_PWD_NKMFD }}
  COCKROACH_DB_CLOUD_DEV_ADDRESS: ${{ secrets.COCKROACH_DB_CLOUD_DEV_ADDRESS_NKMFD }}
  COCKROACH_DB_CLOUD_PROD_USER: ignore
  COCKROACH_DB_CLOUD_PROD_PWD: ignore
  COCKROACH_DB_CLOUD_PROD_ADDRESS: ignore
  RDS_POSTGRESQL_PROD_USER: ignore
  RDS_POSTGRESQL_PROD_PWD: ignore
  RDS_POSTGRESQL_PROD_ADDRESS: ignore
  GIT_TAG: ${{github.ref_name}}
  CR_PAT: ${{secrets.CR_PAT}}
  NAME_SUFFIX: nkmfd-dev
  # curl -O https://www.loggly.com/install/configure-linux.sh
  # sudo bash configure-linux.sh -a fddevserver -t ************************************ -u freedefender
  LOGGLY_PWD: ${{ secrets.LOGGLY_DEV_PWD_NKMFD }}
  LOGGLY_SUBDOMAIN: fddevserver
  LOGGLY_USERNAME: freedefender
  LOGGLY_TOKEN: ************************************

jobs:
  pipeline:
    name: Build AMI
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Show Environment
        run: |
          uname -ra
      - name: packer validate packer json
        uses: docker://hashicorp/packer:1.6.4
        with:
          args: validate packer/amazon-docker.json
      - name: packer inspect packer json
        uses: docker://hashicorp/packer:1.6.4
        with:
          args: inspect packer/amazon-docker.json
      - name: packer build packer json
        uses: docker://hashicorp/packer:1.6.4
        with:
          args: build packer/amazon-docker.json


