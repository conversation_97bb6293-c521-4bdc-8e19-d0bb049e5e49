name: "0 Nkmad Terraform prod CD"
on:
  workflow_dispatch:
    inputs:
      docker-tags:
        required: false
        description: 'NOT IN USE Docker Tag'
        default: 'unused'
jobs:
  terraform:
    name: "Terraform"
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./terraform/nkmad/nkmad-prod-env
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "1.4.6"
          cli_config_credentials_token: ${{ secrets.TF_API_TOKEN }}

      - name: Terraform Init
        id: init
        run: terraform init

      - name: Terraform Plan
        id: plan
        run: terraform plan -no-color
        continue-on-error: true

#      - uses: actions/github-script@0.9.0
#        env:
#          PLAN: "terraform\n${{ steps.plan.outputs.stdout }}"
#        with:
#          github-token: ${{ secrets.GITHUB_TOKEN }}
#          script: |
#            const output = `#### Terraform Format and Style 🖌\`${{ steps.fmt.outcome }}\`
#            #### Terraform Initialization ⚙️\`${{ steps.init.outcome }}\`
#            #### Terraform Plan 📖\`${{ steps.plan.outcome }}\`
#            <details><summary>Show Plan</summary>
#            \`\`\`${process.env.PLAN}\`\`\`
#            </details>
#            *Pusher: @${{ github.actor }}, Action: \`${{ github.event_name }}\`*`;
#
#            github.issues.createComment({
#              issue_number: context.issue.number,
#              owner: context.repo.owner,
#              repo: context.repo.repo,
#              body: output
#            })
      - name: Terraform Plan Status
        if: steps.plan.outcome == 'failure'
        run: exit 1

      - name: Terraform Apply
        run: terraform apply -auto-approve

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEVOPS }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEVOPS }}
          aws-region: us-east-1

      - name: AWS ASG Instance Refresh
        run: |
          aws autoscaling start-instance-refresh --auto-scaling-group-name nkmad-nakama-db-rpc-asg-prod

      - name: Get Terraform Outputs and Set as Step Outputs
        id: get_outputs # 这个步骤的ID
        run: |
          # 获取所有 Terraform 输出并以 JSON 格式打印
          terraform output -json > tf_outputs.json
          
          # 解析 JSON 并将 'game_service_version' 设置为 step output
          GAME_SERVICE_VERSION=$(jq -r '.game_service_version.value' tf_outputs.json)
          
          echo "game_service_version=${GAME_SERVICE_VERSION}" >> $GITHUB_OUTPUT
        shell: bash

      - name: Use Outputs in Subsequent Steps
        run: |
          echo "Game Service Version from previous step: ${{ steps.get_outputs.outputs.game_service_version }}"

      - name: SSH update the realtime server game server version
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.NKMAD_REALTIME_SSH_HOST }}
          username: ${{ secrets.NKMAD_REALTIME_SSH_USERNAME }}
          port: ${{ secrets.NKMAD_REALTIME_SSH_PORT }}
          key: ${{ secrets.NKMAD_REALTIME_SSH_PROD_KEY }}
          script: |
            echo "Connecting to remote server and executing test script..."
            ls -halt
            pwd
            export DB_ADDRESS=${{ secrets.COCKROACH_DB_CLOUD_PROD_ADDRESS_NKMAD }}
            export NAKAMA_PROD_NODE_ID=0
            export NAKAMA_BACKEND_TAG=${{ steps.get_outputs.outputs.game_service_version }}
            docker compose ps
            echo "start to update the game server version"
            cd /home/<USER>/docker-login.sh && docker compose up -d
            docker compose ps