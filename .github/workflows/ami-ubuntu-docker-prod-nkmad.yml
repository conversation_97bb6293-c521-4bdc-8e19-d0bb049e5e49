name: <PERSON><PERSON><PERSON> Packer nkmad Prod Ubuntu Docker Server AMI
on:
#  push:
#    # Sequence of patterns matched against refs/heads
#    branches:
#      # Push events on master branch
#      # - packer
#      # - release
#      # - master
#      # - 0.*.*
#    tags:
#    # - 0.*.*.*-ami          # Push to release tag
#    paths:
#      - 'packer/java-server.json'
#      - 'packer/example.sh'
#      - 'packer/welcome.txt'
  push:
    # Sequence of patterns matched against refs/heads
    tags:
      - 0.*.*-ami-ubt-docker-prod-nkmad          # Push to release tag
# https://hub.docker.com/r/hashicorp/packer/
env:
  # match the github-secret.sh
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_DEVOPS }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEVOPS }}
  AWS_DEFAULT_REGION: us-east-1
  REGION: us-east-1
  INSTALL_REDIS: NO
  NAME: ami-ubt-docker-prod-nkmad
  COCKROACH_DB_CLOUD_DEV_USER: ${{ secrets.COCKROACH_DB_CLOUD_DEV_USER_NKMAD }}
  COCKROACH_DB_CLOUD_DEV_PWD: ${{ secrets.COCKROACH_DB_CLOUD_DEV_PWD_NKMAD }}
  COCKROACH_DB_CLOUD_DEV_ADDRESS: ${{ secrets.COCKROACH_DB_CLOUD_DEV_ADDRESS_NKMAD }}
  COCKROACH_DB_CLOUD_PROD_USER: ${{ secrets.COCKROACH_DB_CLOUD_PROD_USER_NKMAD }}
  COCKROACH_DB_CLOUD_PROD_PWD: ${{ secrets.COCKROACH_DB_CLOUD_PROD_PWD_NKMAD }}
  COCKROACH_DB_CLOUD_PROD_ADDRESS: ${{ secrets.COCKROACH_DB_CLOUD_PROD_ADDRESS_NKMAD }}
  RDS_POSTGRESQL_PROD_USER: ${{ secrets.RDS_POSTGRESQL_PROD_USER_NKMAD }}
  RDS_POSTGRESQL_PROD_PWD: ${{ secrets.RDS_POSTGRESQL_PROD_PWD_NKMAD }}
  RDS_POSTGRESQL_PROD_ADDRESS: ${{ secrets.RDS_POSTGRESQL_PROD_ADDRESS_NKMAD }}
  GIT_TAG: ${{github.ref_name}}
  CR_PAT: ${{secrets.CR_PAT}}
  NAME_SUFFIX: nkmad-prod
  APP: nkmad
  # curl -O https://www.loggly.com/install/configure-linux.sh
  # sudo bash configure-linux.sh -a fdopsserver -t 056e3a18-e51d-4e02-9904-d2e9ecfee467 -u freedefender
  LOGGLY_PWD: ${{ secrets.LOGGLY_PROD_PWD_NKMAD }}
  LOGGLY_SUBDOMAIN: adopsserver
  LOGGLY_USERNAME: atomicdefense
  LOGGLY_TOKEN: ************************************
  # 1panel
  PANEL_LANG_CHOICE: en
  PANEL_PORT: ${{ secrets.PANEL_PORT }}
  PANEL_BASE_DIR: ${{ secrets.PANEL_BASE_DIR }}
  PANEL_ENTRANCE: ${{ secrets.PANEL_ENTRANCE }}
  PANEL_USERNAME: ${{ secrets.PANEL_USERNAME }}
  PANEL_PASSWORD: ${{ secrets.PANEL_PASSWORD }}
jobs:
  pipeline:
    name: Build AMI
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Show Environment
        run: |
          uname -ra
      - name: packer init packer
        uses: docker://hashicorp/packer:1.13.1
        with:
          args: init ./packer
      - name: packer validate packer
        uses: docker://hashicorp/packer:1.13.1
        with:
          args: validate packer/ubuntu-docker.pkr.hcl
      - name: packer inspect packer
        uses: docker://hashicorp/packer:1.13.1
        with:
          args: inspect packer/ubuntu-docker.pkr.hcl
      - name: packer build packer
        uses: docker://hashicorp/packer:1.13.1
        with:
          args: build packer/ubuntu-docker.pkr.hcl


