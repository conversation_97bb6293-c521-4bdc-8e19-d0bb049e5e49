name: "NakamaBase Terraform prod CD"
on:
  workflow_dispatch:
    inputs:
      # logLevel:
      #   description: 'Log level'
      #   required: true
      #   default: 'warning'
      docker-tags:
        required: false
        description: 'Docker Tag'
        default: 'unused'
jobs:
  terraform:
    name: "Terraform"
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./terraform/nakama-backend/nakama-backend-prod-env
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1
        with:
          # terraform_version: 0.13.0:
          cli_config_credentials_token: ${{ secrets.TF_API_TOKEN }}

#      - name: Terraform Format
#        id: fmt
#        run: terraform fmt -check

      - name: Terraform Init
        id: init
        run: terraform init

      - name: Terraform Plan
        id: plan
        run: terraform plan -no-color
        continue-on-error: true

#      - uses: actions/github-script@0.9.0
#        env:
#          PLAN: "terraform\n${{ steps.plan.outputs.stdout }}"
#        with:
#          github-token: ${{ secrets.GITHUB_TOKEN }}
#          script: |
#            const output = `#### Terraform Format and Style 🖌\`${{ steps.fmt.outcome }}\`
#            #### Terraform Initialization ⚙️\`${{ steps.init.outcome }}\`
#            #### Terraform Plan 📖\`${{ steps.plan.outcome }}\`
#            <details><summary>Show Plan</summary>
#            \`\`\`${process.env.PLAN}\`\`\`
#            </details>
#            *Pusher: @${{ github.actor }}, Action: \`${{ github.event_name }}\`*`;
#
#            github.issues.createComment({
#              issue_number: context.issue.number,
#              owner: context.repo.owner,
#              repo: context.repo.repo,
#              body: output
#            })
      - name: Terraform Plan Status
        if: steps.plan.outcome == 'failure'
        run: exit 1

      - name: Terraform Apply
        run: terraform apply -auto-approve

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEVOPS }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEVOPS }}
          aws-region: us-east-1

      - name: AWS ASG Instance Refresh
        run: |
          aws autoscaling start-instance-refresh --auto-scaling-group-name nakamabase_prod
