name: <PERSON><PERSON><PERSON> Packer nkmmd Live Amazon Docker Server AMI
on:
#  push:
#    # Sequence of patterns matched against refs/heads
#    branches:
#      # Push events on master branch
#      # - packer
#      # - release
#      # - master
#      # - 0.*.*
#    tags:
#    # - 0.*.*.*-ami          # Push to release tag
#    paths:
#      - 'packer/java-server.json'
#      - 'packer/example.sh'
#      - 'packer/welcome.txt'
  push:
    # Sequence of patterns matched against refs/heads
    tags:
      - 0.*.*-ami-amz-docker-live-nkmmd          # Push to release tag
# https://hub.docker.com/r/hashicorp/packer/
env:
  # match the github-secret.sh
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_DEVOPS }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEVOPS }}
  AWS_DEFAULT_REGION: us-east-1
  REGION: us-east-1
  INSTALL_REDIS: NO
  NAME: ami-amz-docker-live-nkmmd
  COCKROACH_DB_CLOUD_DEV_USER: ${{ secrets.COCKROACH_DB_CLOUD_DEV_USER_NKMMD }}
  COCKROACH_DB_CLOUD_DEV_PWD: ${{ secrets.COCKROACH_DB_CLOUD_DEV_PWD_NKMMD }}
  COCKROACH_DB_CLOUD_DEV_ADDRESS: ${{ secrets.COCKROACH_DB_CLOUD_DEV_ADDRESS_NKMMD }}
  COCKROACH_DB_CLOUD_PROD_USER: ${{ secrets.COCKROACH_DB_CLOUD_PROD_USER_NKMMD }}
  COCKROACH_DB_CLOUD_PROD_PWD: ${{ secrets.COCKROACH_DB_CLOUD_PROD_PWD_NKMMD }}
  COCKROACH_DB_CLOUD_PROD_ADDRESS: ${{ secrets.COCKROACH_DB_CLOUD_PROD_ADDRESS_NKMMD }}
  RDS_POSTGRESQL_PROD_USER: ${{ secrets.RDS_POSTGRESQL_PROD_USER_NKMMD }}
  RDS_POSTGRESQL_PROD_PWD: ${{ secrets.RDS_POSTGRESQL_PROD_PWD_NKMMD }}
  RDS_POSTGRESQL_PROD_ADDRESS: ${{ secrets.RDS_POSTGRESQL_PROD_ADDRESS_NKMMD }}
  GIT_TAG: ${{github.ref_name}}
  CR_PAT: ${{secrets.CR_PAT}}
  NAME_SUFFIX: nkmmd

jobs:
  pipeline:
    name: Build AMI
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Show Environment
        run: |
          uname -ra
      - name: packer validate packer json
        uses: docker://hashicorp/packer:1.6.4
        with:
          args: validate packer/amazon-docker.json
      - name: packer inspect packer json
        uses: docker://hashicorp/packer:1.6.4
        with:
          args: inspect packer/amazon-docker.json
      - name: packer build packer json
        uses: docker://hashicorp/packer:1.6.4
        with:
          args: build packer/amazon-docker.json


