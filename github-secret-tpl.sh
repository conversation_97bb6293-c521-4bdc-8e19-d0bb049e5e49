#!/usr/bin/env sh
# How to use?
# 1. copy the github-secret-tpl.json to github-secret.json
# 2. update the github-secret.json with some secret value
# 3. run this script to set the secret to the GitHub repository

## Proxy Setup for GitHub CLI
#export HTTP_PROXY=http://127.0.0.1:29758
#export HTTPS_PROXY=http://127.0.0.1:29758

gh secret list
#  gh-action channel income-webhook: https://playwind.slack.com/apps/A0F7XDUAZ-incoming-webhooks?tab=more_info
#gh secret set SLACK_WEBHOOK -b""
# Ask <PERSON><PERSON> to get this token for run jenkins job on self-host runner
#gh secret set JENKINS_TOKEN_GITHUB_AT_PW -b""
################## FOR RM ##################
######### Because we still use cg s3 for maven, so still keep use cg aws credential
# gh secret set RM_AWS_ACCESS_KEY_ID -b""
# gh secret set RM_AWS_SECRET_ACCESS_KEY -b""
######### We need to setup the new RM EC2 instance information
gh secret set AWS_ACCESS_KEY_ID_DEVOPS -b""
gh secret set AWS_SECRET_ACCESS_KEY_DEVOPS -b""
# https://app.terraform.io/app/settings/tokens personal token of devopsga
gh secret set TF_API_TOKEN -b""
# container register personal app token
gh secret set CR_PAT -b""

# COCKROACH CLOUD Related parameters
gh secret set COCKROACH_DB_CLOUD_DEV_USER -b""
gh secret set COCKROACH_DB_CLOUD_DEV_PWD -b""
gh secret set COCKROACH_DB_CLOUD_DEV_ADDRESS -b"[USERNAME]:[PWD]@playwind-cn-dev-630.j77.cockroachlabs.cloud:26257/defaultdb?sslmode=verify-full"

gh secret set COCKROACH_DB_CLOUD_PROD_USER -b""
gh secret set COCKROACH_DB_CLOUD_PROD_PWD -b""
gh secret set COCKROACH_DB_CLOUD_PROD_ADDRESS -b"[USERNAME]:[PWD]@playwind-cn-prod-721.j77.cockroachlabs.cloud:26257/defaultdb?sslmode=verify-full"

# after RDS setup, set here
gh secret set RDS_POSTGRESQL_PROD_USER -b""
# after RDS setup, set here
gh secret set RDS_POSTGRESQL_PROD_PWD -b""
gh secret set RDS_POSTGRESQL_PROD_ADDRESS -b"[USERNAME]:[PWD]@postgres:5432/nakama_backend_prod"

###############  nkmmd start #################
# COCKROACH CLOUD Related parameters
gh secret set COCKROACH_DB_CLOUD_DEV_USER_NKMMD -b""
gh secret set COCKROACH_DB_CLOUD_DEV_PWD_NKMMD -b""
gh secret set COCKROACH_DB_CLOUD_DEV_ADDRESS_NKMMD -b"[USERNAME]:[PWD]@playwind-cn-dev-630.j77.cockroachlabs.cloud:26257/defaultdb?sslmode=verify-full"

gh secret set COCKROACH_DB_CLOUD_PROD_USER_NKMMD -b""
gh secret set COCKROACH_DB_CLOUD_PROD_PWD_NKMMD -b""
gh secret set COCKROACH_DB_CLOUD_PROD_ADDRESS_NKMMD -b"[USERNAME]:[PWD]@playwind-cn-prod-721.j77.cockroachlabs.cloud:26257/defaultdb?sslmode=verify-full"

# after RDS setup, set here
gh secret set RDS_POSTGRESQL_PROD_USER_NKMMD -b""
# after RDS setup, set here
gh secret set RDS_POSTGRESQL_PROD_PWD_NKMMD -b""
gh secret set RDS_POSTGRESQL_PROD_ADDRESS_NKMMD -b"[USERNAME]:[PWD]@postgres:5432/nakama_backend_prod"
###############  nkmmd end   #################

###############  nkmfd start #################
# COCKROACH CLOUD Related parameters
gh secret set COCKROACH_DB_CLOUD_DEV_USER_NKMFD -b"freedefender_dev"
gh secret set COCKROACH_DB_CLOUD_DEV_PWD_NKMFD -b"xxx"
gh secret set COCKROACH_DB_CLOUD_DEV_ADDRESS_NKMFD -b"freedefender_dev:@freedefender-dev-1926.j77.cockroachlabs.cloud:26257/defaultdb?sslmode=verify-full"

gh secret set COCKROACH_DB_CLOUD_PROD_USER_NKMFD -b"freedefender_ops"
gh secret set COCKROACH_DB_CLOUD_PROD_PWD_NKMFD -b"xxx"
gh secret set COCKROACH_DB_CLOUD_PROD_ADDRESS_NKMFD -b"freedefender_ops:<EMAIL>:26257/defaultdb?sslmode=verify-full"

# after RDS setup, set here
gh secret set RDS_POSTGRESQL_PROD_USER_NKMFD -b"nkmfd_admin"
# after RDS setup, set here
gh secret set RDS_POSTGRESQL_PROD_PWD_NKMFD -b"xxx"
gh secret set RDS_POSTGRESQL_PROD_ADDRESS_NKMFD -b"nkmfd_admin:<EMAIL>:5432/nkmfd_db"

gh secret set LOGGLY_DEV_PWD_NKMFD -b"P...#w...2021.."
gh secret set LOGGLY_PROD_PWD_NKMFD -b"P...#w...2021.."
###############  nkmfd end   #################


###############  nkmad start #################
###### 如果切换数据库, 需要更新这些环境变量, 然后重新 build packer, 当前的运行的节点可以通过 auto scaling 可以重新构建, 也可以手动修改环境变量
gh secret set COCKROACH_DB_CLOUD_PROD_USER_NKMAD -b"" --repo playwindgames/devops-nakama
gh secret set COCKROACH_DB_CLOUD_PROD_PWD_NKMAD -b"" --repo playwindgames/devops-nakama
gh secret set COCKROACH_DB_CLOUD_PROD_ADDRESS_NKMAD -b"" --repo playwindgames/devops-nakama

gh secret set LOGGLY_DEV_PWD_NKMAD -b"#" --repo playwindgames/devops-nakama
gh secret set LOGGLY_PROD_PWD_NKMAD -b"#" --repo playwindgames/devops-nakama

#1Panel
# http://**************:24914/tXTAGMf9P1
# http://prod-node-0.nkmad.pwglab.com:24914/tXTAGMf9P1
gh variable set LANG_CHOICE -b"en" --repo playwindgames/devops-nakama
gh secret set PANEL_PORT -b"" --repo playwindgames/devops-nakama
gh secret set PANEL_BASE_DIR -b"/" --repo playwindgames/devops-nakama
gh secret set PANEL_ENTRANCE -b"" --repo playwindgames/devops-nakama
gh secret set PANEL_USERNAME -b"" --repo playwindgames/devops-nakama
gh secret set PANEL_PASSWORD -b"" --repo playwindgames/devops-nakama


gh secret set NKMAD_REALTIME_SSH_HOST -b"xx" --repo playwindgames/devops-nakama
gh secret set NKMAD_REALTIME_SSH_PORT -b"xx" --repo playwindgames/devops-nakama
gh secret set NKMAD_REALTIME_SSH_USERNAME -b"xx" --repo playwindgames/devops-nakama
gh secret set NKMAD_REALTIME_SSH_PROD_KEY -b"-----BEGIN RSA PRIVATE KEY-----
xxx
-----END RSA PRIVATE KEY-----" --repo playwindgames/devops-nakama
###############  nkmad end #################
gh secret list
